import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/date_symbol_data_local.dart';

// استيراد الخدمات الموجودة
import 'services/theme_provider.dart';
import 'services/quran_provider.dart';
import 'services/statistics_provider.dart';

// استيراد الشاشات الموجودة
import 'screens/home_screen.dart';

void main() async {
  try {
    // ضروري لاستخدام الميزات الأصلية قبل تشغيل runApp
    WidgetsFlutterBinding.ensureInitialized();

    // تحسين استهلاك الذاكرة للصور - منع التهنيج
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 << 20; // 50 MB
    PaintingBinding.instance.imageCache.maximumSize = 100; // حد أقصى 100 صورة

    // تهيئة بيانات اللغة العربية
    await initializeLocalization();

    // تعيين اتجاهات الشاشة المفضلة (عمودي فقط)
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // تعيين وضع واجهة النظام لإظهار شريط الإشعارات بشكل طبيعي
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    // تشغيل التطبيق
    runApp(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (context) => ThemeProvider()),
          ChangeNotifierProvider(create: (context) => QuranProvider()),
          ChangeNotifierProvider(create: (context) => StatisticsProvider()),
        ],
        child: const MyApp(),
      ),
    );
  } catch (e) {
    // معالجة أي أخطاء قد تحدث أثناء التهيئة
    debugPrint('Error occurred during initialization: $e');

    // تشغيل التطبيق بدون تهيئة إضافية في حالة حدوث خطأ
    runApp(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (context) => ThemeProvider()),
          ChangeNotifierProvider(create: (context) => QuranProvider()),
          ChangeNotifierProvider(create: (context) => StatisticsProvider()),
        ],
        child: const MyApp(),
      ),
    );
  }
}

// دالة لتهيئة بيانات اللغة العربية
Future<void> initializeLocalization() async {
  // تهيئة بيانات اللغة العربية
  // هذه الخطوة ضرورية لتجنب خطأ LocaleDataException
  await initializeDateFormatting('ar_SA', null);
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    // استخدام Consumer للاستماع إلى تغييرات مزود السمة
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return MaterialApp(
          title: 'أذكاري',
          debugShowCheckedModeBanner: false,
          // استخدام السمات من مزود السمة
          theme: ThemeData(
            primarySwatch: Colors.blue,
            fontFamily: 'Amiri',
          ),
          darkTheme: ThemeData.dark().copyWith(
            primaryColor: Colors.blue,
          ),
          themeMode: ThemeMode.system,
          // تعيين اللغة العربية كلغة افتراضية
          locale: const Locale('ar', 'SA'),
          // إضافة دعم اللغة العربية
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          // تحديد اللغات المدعومة
          supportedLocales: const [
            Locale('ar', 'SA'), // العربية (السعودية)
            Locale('ar', 'EG'), // العربية (مصر)
            Locale('en', 'US'), // الإنجليزية (الولايات المتحدة)
          ],
          home: const HomeScreen(),
        );
      },
    );
  }
}
