import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'models/theme_mode_enum.dart';
import 'services/azkar_provider.dart';
import 'services/notification_service.dart';
import 'services/theme_provider.dart';
import 'services/tasbih_provider.dart';
import 'services/feedback_provider.dart';
import 'services/quran_provider.dart';
import 'services/daily_ayah_provider.dart';
import 'services/statistics_provider.dart';
import 'services/prayer_provider.dart';
import 'services/daily_question_service.dart';
import 'screens/main_screen.dart';
import 'screens/splash_screen.dart';
import 'utils/platform_helper.dart';
import 'utils/logger.dart';
import 'widgets/page_transitions.dart';

// متغير للتحقق من وجود خطأ في Platform
bool _hasPlatformError = false;

void main() async {
  try {
    // ضروري لاستخدام الميزات الأصلية قبل تشغيل runApp
    WidgetsFlutterBinding.ensureInitialized();

    // تهيئة قاعدة البيانات
    await _initializeDatabase();

    // تهيئة بيانات اللغة العربية
    await initializeLocalization();

    // اختبار وجود خطأ في Platform
    try {
      // اختبار استخدام PlatformHelper
      PlatformHelper.isAndroid;
      _hasPlatformError = false;
    } catch (e) {
      _hasPlatformError = true;
      AppLogger.error('Platform error detected: $e', e);
    }

    // تهيئة الإشعارات (فقط إذا لم يكن هناك خطأ في Platform)
    if (!_hasPlatformError) {
      await NotificationService().init();
    }

    // تعيين اتجاهات الشاشة المفضلة (عمودي فقط)
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // تعيين وضع واجهة النظام لإظهار شريط الإشعارات بشكل طبيعي
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    // تهيئة مزود الآية اليومية
    final dailyAyahProvider = DailyAyahProvider();
    await dailyAyahProvider.initialize();

    // تهيئة خدمة الأسئلة اليومية
    final dailyQuestionService = DailyQuestionService();
    await dailyQuestionService.initialize();

    // تشغيل التطبيق
    runApp(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (context) => StatisticsProvider()),
          ChangeNotifierProxyProvider<StatisticsProvider, AzkarProvider>(
            create: (context) => AzkarProvider(),
            update: (context, statisticsProvider, azkarProvider) {
              azkarProvider?.setStatisticsProvider(statisticsProvider);
              return azkarProvider ?? AzkarProvider();
            },
          ),
          ChangeNotifierProvider(create: (context) => ThemeProvider()),
          ChangeNotifierProvider(create: (context) => TasbihProvider()),
          ChangeNotifierProvider(create: (context) => FeedbackProvider()),
          ChangeNotifierProvider(create: (context) => QuranProvider()),
          ChangeNotifierProvider(create: (context) => PrayerProvider()),
          ChangeNotifierProvider.value(value: dailyAyahProvider),
          ChangeNotifierProvider.value(value: dailyQuestionService),
        ],
        child: const MyApp(),
      ),
    );
  } catch (e) {
    // معالجة أي أخطاء قد تحدث أثناء التهيئة
    AppLogger.error('Error occurred during initialization: $e', e);

    // تهيئة مزود الآية اليومية
    final dailyAyahProvider = DailyAyahProvider();

    // تهيئة خدمة الأسئلة اليومية
    final dailyQuestionService = DailyQuestionService();

    // تشغيل التطبيق بدون تهيئة إضافية في حالة حدوث خطأ
    runApp(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (context) => StatisticsProvider()),
          ChangeNotifierProxyProvider<StatisticsProvider, AzkarProvider>(
            create: (context) => AzkarProvider(),
            update: (context, statisticsProvider, azkarProvider) {
              azkarProvider?.setStatisticsProvider(statisticsProvider);
              return azkarProvider ?? AzkarProvider();
            },
          ),
          ChangeNotifierProvider(create: (context) => ThemeProvider()),
          ChangeNotifierProvider(create: (context) => TasbihProvider()),
          ChangeNotifierProvider(create: (context) => FeedbackProvider()),
          ChangeNotifierProvider(create: (context) => QuranProvider()),
          ChangeNotifierProvider(create: (context) => PrayerProvider()),
          ChangeNotifierProvider.value(value: dailyAyahProvider),
          ChangeNotifierProvider.value(value: dailyQuestionService),
        ],
        child: const MyApp(),
      ),
    );
  }
}

// دالة لتهيئة قاعدة البيانات
Future<void> _initializeDatabase() async {
  try {
    AppLogger.info('بدء تهيئة قاعدة البيانات...');

    // تهيئة sqflite_ffi للمنصات المكتبية
    sqfliteFfiInit();

    // تعيين databaseFactory إلى databaseFactoryFfi
    // هذا ضروري لاستخدام sqflite_common_ffi
    databaseFactory = databaseFactoryFfi;

    AppLogger.info('تم تهيئة قاعدة البيانات بنجاح');
  } catch (e) {
    AppLogger.error('خطأ في تهيئة قاعدة البيانات: $e');
  }
}

// دالة لتهيئة بيانات اللغة العربية
Future<void> initializeLocalization() async {
  // تهيئة بيانات اللغة العربية
  // هذه الخطوة ضرورية لتجنب خطأ LocaleDataException
  await initializeDateFormatting('ar_SA', null);
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // La visualización de la Aleya diaria ahora se maneja desde MainScreen
  }

  /// تحويل وضع الثيم إلى ThemeMode
  ThemeMode _getThemeMode(AppThemeMode themeMode) {
    switch (themeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dim:
      case AppThemeMode.dark:
        return ThemeMode.dark;
    }
  }

  @override
  Widget build(BuildContext context) {
    // استخدام Consumer للاستماع إلى تغييرات مزود السمة
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return MaterialApp(
          title: 'أذكاري',
          debugShowCheckedModeBanner: false,
          // استخدام السمات من مزود السمة
          theme: themeProvider.lightTheme,
          darkTheme: themeProvider.currentTheme,
          themeMode: _getThemeMode(themeProvider.themeMode),
          // طباعة معلومات السمة للتأكد من تطبيقها
          builder: (context, child) {
            // طباعة معلومات السمة عند بناء التطبيق
            AppLogger.info(
              'Building app with theme mode: ${themeProvider.themeMode.toString().split('.').last}',
            );
            return child!;
          },
          // تعيين اللغة العربية كلغة افتراضية
          locale: const Locale('ar', 'SA'),
          // إضافة دعم اللغة العربية
          localizationsDelegates: [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          // تحديد اللغات المدعومة
          supportedLocales: const [
            Locale('ar', 'SA'), // العربية (السعودية)
            Locale('ar', 'EG'), // العربية (مصر)
            Locale('en', 'US'), // الإنجليزية (الولايات المتحدة)
          ],
          // إضافة تأثيرات الانتقال بين الصفحات
          onGenerateRoute: (settings) {
            // الحصول على الصفحة المطلوبة
            Widget page;

            // تحديد الصفحة بناءً على الاسم
            if (settings.name == '/') {
              page = const MainScreen(showDailyAyah: false);
            } else {
              // الصفحة الافتراضية
              page = const MainScreen(showDailyAyah: false);
            }

            // إنشاء انتقال بين الصفحات
            return PageTransition<dynamic>(
              child: page,
              type: PageTransitionType.rightToLeftWithFade,
              settings: settings,
              duration: const Duration(milliseconds: 400),
              curve: Curves.easeInOut,
            );
          },
          home: const SplashScreen(),
        );
      },
    );
  }
}
