import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/tasbih_model.dart';
import 'tasbih_service.dart';
import '../utils/logger.dart';

/// مزود التسبيحات
class TasbihProvider extends ChangeNotifier {
  final TasbihService _tasbihService = TasbihService();

  List<Tasbih> _tasbihs = [];
  Tasbih? _currentTasbih;
  Map<String, int> _stats = {'today': 0, 'week': 0, 'total': 0};
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _isLoading = true;

  // الحصول على قائمة التسبيحات
  List<Tasbih> get tasbihs => _tasbihs;

  // الحصول على التسبيحة الحالية
  Tasbih? get currentTasbih => _currentTasbih;

  // الحصول على إحصائيات التسبيح
  Map<String, int> get stats => _stats;

  // الحصول على حالة تفعيل الصوت
  bool get soundEnabled => _soundEnabled;

  // الحصول على حالة تفعيل الاهتزاز
  bool get vibrationEnabled => _vibrationEnabled;

  // الحصول على حالة التحميل
  bool get isLoading => _isLoading;

  // المُنشئ - يقوم بتحميل البيانات عند إنشاء المزود
  TasbihProvider() {
    _loadData();
  }

  // تحميل البيانات
  Future<void> _loadData() async {
    _isLoading = true;
    notifyListeners();

    try {
      // تحميل التسبيحات
      _tasbihs = await _tasbihService.getTasbihs();

      // تعيين التسبيحة الحالية إلى أول تسبيحة إذا كانت القائمة غير فارغة
      if (_tasbihs.isNotEmpty) {
        _currentTasbih = _tasbihs.first;
      }

      // تحميل الإحصائيات
      _stats = await _tasbihService.getStats();

      // تحميل إعدادات الصوت والاهتزاز
      _soundEnabled = await _tasbihService.getSoundEnabled();
      _vibrationEnabled = await _tasbihService.getVibrationEnabled();
    } catch (e) {
      AppLogger.error('خطأ في تحميل البيانات: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  // تعيين التسبيحة الحالية
  void setCurrentTasbih(Tasbih tasbih) {
    _currentTasbih = tasbih;
    notifyListeners();
  }

  // حدث يتم إطلاقه عند اكتمال التسبيحة
  final completionController = StreamController<Tasbih>.broadcast();
  Stream<Tasbih> get onTasbihCompleted => completionController.stream;

  // زيادة عداد التسبيح
  Future<void> incrementCount() async {
    if (_currentTasbih == null) return;

    // التحقق من عدم تجاوز العدد المستهدف
    if (_currentTasbih!.count >= _currentTasbih!.targetCount) {
      // إذا كان العداد قد وصل بالفعل إلى العدد المستهدف، نتأكد من تحديث حالة الاكتمال
      if (!_currentTasbih!.isCompleted) {
        final updatedTasbih = _currentTasbih!.copyWith(isCompleted: true);

        // تحديث التسبيحة في القائمة
        final index = _tasbihs.indexWhere(
          (tasbih) => tasbih.id == updatedTasbih.id,
        );
        if (index != -1) {
          _tasbihs[index] = updatedTasbih;
        }

        // تحديث التسبيحة الحالية
        _currentTasbih = updatedTasbih;

        // إطلاق حدث اكتمال التسبيحة
        completionController.add(updatedTasbih);

        // حفظ التغييرات
        await _tasbihService.updateTasbih(updatedTasbih);

        notifyListeners();
      }
      return; // لا تزيد العداد إذا وصل إلى العدد المستهدف
    }

    // تفعيل الاهتزاز إذا كان مفعلاً
    if (_vibrationEnabled) {
      HapticFeedback.lightImpact();
    }

    // زيادة العداد
    final updatedTasbih = _currentTasbih!.incrementCount();

    // التحقق مما إذا كانت التسبيحة قد اكتملت
    final wasCompleted = updatedTasbih.count >= updatedTasbih.targetCount;

    // تحديث التسبيحة في القائمة
    final index = _tasbihs.indexWhere(
      (tasbih) => tasbih.id == updatedTasbih.id,
    );
    if (index != -1) {
      _tasbihs[index] = updatedTasbih;
    }

    // تحديث التسبيحة الحالية
    _currentTasbih = updatedTasbih;

    // إذا اكتملت التسبيحة، أطلق حدث الاكتمال
    if (wasCompleted) {
      // إطلاق حدث اكتمال التسبيحة
      completionController.add(updatedTasbih);
    }

    // تحديث الإحصائيات
    await _tasbihService.updateStats(1);
    _stats['today'] = (_stats['today'] ?? 0) + 1;
    _stats['week'] = (_stats['week'] ?? 0) + 1;
    _stats['total'] = (_stats['total'] ?? 0) + 1;

    // حفظ التغييرات
    await _tasbihService.updateTasbih(updatedTasbih);

    notifyListeners();
  }

  // إعادة ضبط العداد
  Future<void> resetCount() async {
    if (_currentTasbih == null) return;

    // إعادة ضبط العداد
    final updatedTasbih = _currentTasbih!.resetCount();

    // تحديث التسبيحة في القائمة
    final index = _tasbihs.indexWhere(
      (tasbih) => tasbih.id == updatedTasbih.id,
    );
    if (index != -1) {
      _tasbihs[index] = updatedTasbih;
    }

    // تم إزالة التعامل مع قائمة التسبيحات المكتملة

    // تحديث التسبيحة الحالية
    _currentTasbih = updatedTasbih;

    // حفظ التغييرات
    await _tasbihService.updateTasbih(updatedTasbih);

    notifyListeners();
  }

  // إضافة تسبيحة جديدة
  Future<void> addTasbih(String text, int targetCount) async {
    final newTasbih = await _tasbihService.addTasbih(text, targetCount);
    if (newTasbih != null) {
      _tasbihs.add(newTasbih);
      notifyListeners();
    }
  }

  // تحديث تسبيحة
  Future<void> updateTasbih(Tasbih tasbih) async {
    final success = await _tasbihService.updateTasbih(tasbih);
    if (success) {
      final index = _tasbihs.indexWhere((item) => item.id == tasbih.id);
      if (index != -1) {
        _tasbihs[index] = tasbih;

        // تحديث التسبيحة الحالية إذا كانت هي التي تم تحديثها
        if (_currentTasbih != null && _currentTasbih!.id == tasbih.id) {
          _currentTasbih = tasbih;
        }

        notifyListeners();
      }
    }
  }

  // حذف تسبيحة
  Future<void> deleteTasbih(int id) async {
    final success = await _tasbihService.deleteTasbih(id);
    if (success) {
      _tasbihs.removeWhere((tasbih) => tasbih.id == id);

      // إذا كانت التسبيحة الحالية هي التي تم حذفها، قم بتعيين التسبيحة الحالية إلى أول تسبيحة
      if (_currentTasbih != null && _currentTasbih!.id == id) {
        _currentTasbih = _tasbihs.isNotEmpty ? _tasbihs.first : null;
      }

      notifyListeners();
    }
  }

  // تبديل حالة المفضلة للتسبيحة
  Future<void> toggleFavorite(Tasbih tasbih) async {
    final updatedTasbih = tasbih.toggleFavorite();
    await updateTasbih(updatedTasbih);
  }

  // تعيين حالة تفعيل الصوت
  Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    await _tasbihService.setSoundEnabled(enabled);
    notifyListeners();
  }

  // تعيين حالة تفعيل الاهتزاز
  Future<void> setVibrationEnabled(bool enabled) async {
    _vibrationEnabled = enabled;
    await _tasbihService.setVibrationEnabled(enabled);
    notifyListeners();
  }

  // إغلاق الموارد عند التخلص من المزود
  @override
  void dispose() {
    completionController.close();
    super.dispose();
  }
}
