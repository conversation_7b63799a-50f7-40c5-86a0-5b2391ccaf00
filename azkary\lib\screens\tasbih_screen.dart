import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/tasbih_model.dart';
import '../services/tasbih_provider.dart';
import '../services/feedback_provider.dart';
import '../widgets/islamic_pattern.dart';
import '../widgets/islamic_background.dart';
import '../widgets/custom_app_bar.dart';

class TasbihScreen extends StatefulWidget {
  const TasbihScreen({super.key});

  @override
  State<TasbihScreen> createState() => _TasbihScreenState();
}

class _TasbihScreenState extends State<TasbihScreen>
    with TickerProviderStateMixin {
  // متغيرات الحركة الانتقالية للضغط على زر التسبيح
  late AnimationController _animationController;
  late Animation<double> _animation;

  // متغيرات الحركة الانتقالية لكلمة "أحسنت"
  late AnimationController _congratsController;
  late Animation<double> _congratsScaleAnimation;
  late Animation<double> _congratsOpacityAnimation;
  late Animation<Color?> _congratsColorAnimation;
  late Animation<double> _congratsPulseAnimation;

  // متغير لتتبع ما إذا كانت الحركة الانتقالية لكلمة "أحسنت" قد بدأت
  bool _isCongratsAnimationStarted = false;

  // الاشتراك في حدث اكتمال التسبيحة
  StreamSubscription<Tasbih>? _completionSubscription;

  @override
  void initState() {
    super.initState();

    // إعداد حركة الضغط على زر التسبيح
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // إعداد حركة كلمة "أحسنت"
    _congratsController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // حركة تكبير وتصغير كلمة "أحسنت"
    _congratsScaleAnimation = TweenSequence<double>([
      TweenSequenceItem(tween: Tween<double>(begin: 0.5, end: 1.1), weight: 30),
      TweenSequenceItem(tween: Tween<double>(begin: 1.1, end: 1.0), weight: 20),
      TweenSequenceItem(tween: Tween<double>(begin: 1.0, end: 1.0), weight: 50),
    ]).animate(
      CurvedAnimation(parent: _congratsController, curve: Curves.easeInOut),
    );

    // حركة ظهور واختفاء كلمة "أحسنت"
    _congratsOpacityAnimation = TweenSequence<double>([
      TweenSequenceItem(tween: Tween<double>(begin: 0.2, end: 1.0), weight: 20),
      TweenSequenceItem(tween: Tween<double>(begin: 1.0, end: 1.0), weight: 60),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 0.95),
        weight: 20,
      ),
    ]).animate(
      CurvedAnimation(parent: _congratsController, curve: Curves.easeInOut),
    );

    // حركة تغير لون كلمة "أحسنت" - تحسين التأثير البصري
    _congratsColorAnimation = TweenSequence<Color?>([
      TweenSequenceItem(
        tween: ColorTween(begin: Colors.white, end: Colors.white),
        weight: 30,
      ),
      TweenSequenceItem(
        tween: ColorTween(
          begin: Colors.white,
          end: Colors.white.withGreen(240),
        ),
        weight: 30,
      ),
      TweenSequenceItem(
        tween: ColorTween(
          begin: Colors.white.withGreen(240),
          end: Colors.white,
        ),
        weight: 40,
      ),
    ]).animate(
      CurvedAnimation(parent: _congratsController, curve: Curves.easeInOut),
    );

    // حركة نبض كلمة "أحسنت"
    _congratsPulseAnimation = TweenSequence<double>([
      TweenSequenceItem(tween: Tween<double>(begin: 1.0, end: 1.1), weight: 10),
      TweenSequenceItem(tween: Tween<double>(begin: 1.1, end: 1.0), weight: 10),
      TweenSequenceItem(tween: Tween<double>(begin: 1.0, end: 1.1), weight: 10),
      TweenSequenceItem(tween: Tween<double>(begin: 1.1, end: 1.0), weight: 10),
      TweenSequenceItem(tween: Tween<double>(begin: 1.0, end: 1.0), weight: 60),
    ]).animate(
      CurvedAnimation(parent: _congratsController, curve: Curves.easeInOut),
    );

    // إضافة مستمع للحركة الانتقالية لتكرارها
    _congratsController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // إعادة تشغيل الحركة الانتقالية بعد انتهائها
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted && _isCongratsAnimationStarted) {
            // تجنب إعادة تشغيل الحركة إذا تم التخلص من الحالة
            try {
              _congratsController.reset();
              _congratsController.forward();
            } catch (e) {
              // تجاهل الأخطاء التي قد تحدث إذا تم التخلص من الحالة
            }
          }
        });
      }
    });

    // الاشتراك في حدث اكتمال التسبيحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final tasbihProvider = Provider.of<TasbihProvider>(
        context,
        listen: false,
      );
      _completionSubscription = tasbihProvider.onTasbihCompleted.listen(
        _handleTasbihCompleted,
      );
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _congratsController.dispose();
    _completionSubscription?.cancel();
    super.dispose();
  }

  // معالجة حدث اكتمال التسبيحة
  void _handleTasbihCompleted(Tasbih tasbih) {
    if (mounted) {
      setState(() {
        _isCongratsAnimationStarted = true;
      });

      // تنفيذ اهتزاز قوي عند اكتمال التسبيحة
      final feedbackProvider = Provider.of<FeedbackProvider>(
        context,
        listen: false,
      );
      feedbackProvider.heavyHapticFeedback();

      // تأكد من أن المتحكم جاهز قبل تشغيل الحركة
      try {
        if (_congratsController.isCompleted ||
            _congratsController.isDismissed) {
          _congratsController.reset();
          _congratsController.forward();
        }
      } catch (e) {
        // تجاهل الأخطاء التي قد تحدث إذا تم التخلص من الحالة
      }
    }
  }

  void _playPressAnimation() {
    _animationController.reset();
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: CustomAppBar(
        title: 'المسبحة الإلكترونية',
        actions: [
          // زر إضافة ذكر
          IconButton(
            icon: Icon(Icons.add, color: theme.colorScheme.onSurface, size: 24),
            tooltip: 'إضافة تسبيحة',
            onPressed: () {
              HapticFeedback.lightImpact(); // تأثير اهتزاز خفيف
              _showAddTasbihDialog(context);
            },
          ),
          // زر الإعدادات
          IconButton(
            icon: Icon(
              Icons.settings,
              color: theme.colorScheme.onSurface,
              size: 24,
            ),
            tooltip: 'الإعدادات',
            onPressed: () {
              HapticFeedback.lightImpact(); // تأثير اهتزاز خفيف
              _showSettingsDialog(context);
            },
          ),
        ],
      ),
      body: IslamicBackground(
        opacity: isDarkMode ? 0.02 : 0.03,
        showPattern: true,
        showGradient: false,
        child: Consumer<TasbihProvider>(
          builder: (context, tasbihProvider, child) {
            if (tasbihProvider.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (tasbihProvider.currentTasbih == null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'لا توجد تسبيحات',
                      style: TextStyle(fontSize: 18),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => _showAddTasbihDialog(context),
                      child: const Text('إضافة تسبيحة'),
                    ),
                  ],
                ),
              );
            }

            final currentTasbih = tasbihProvider.currentTasbih!;
            final progress = currentTasbih.count / currentTasbih.targetCount;
            final isCompleted =
                currentTasbih.count >= currentTasbih.targetCount;

            return Stack(
              children: [
                // زخارف إسلامية في الخلفية
                Positioned.fill(
                  child: Opacity(
                    opacity: isDarkMode ? 0.05 : 0.1,
                    child: const IslamicPattern(),
                  ),
                ),

                // استخدام SingleChildScrollView لمنع تجاوز المحتوى لحجم الشاشة
                SingleChildScrollView(
                  child: Column(
                    children: [
                      // آية قرآنية أو حديث
                      Container(
                        padding: const EdgeInsets.all(16),
                        margin: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color:
                              isDarkMode
                                  ? Colors.black12
                                  : theme.colorScheme.primary.withAlpha(25),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color:
                                isDarkMode
                                    ? Colors.white24
                                    : theme.colorScheme.primary.withAlpha(75),
                            width: 1.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color:
                                  isDarkMode
                                      ? Colors.black12
                                      : theme.colorScheme.primary.withAlpha(15),
                              blurRadius: 8,
                              spreadRadius: 1,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            Text(
                              'أَلَا بِذِكْرِ اللَّهِ تَطْمَئِنُّ الْقُلُوبُ',
                              style: TextStyle(
                                fontSize:
                                    Theme.of(context)
                                        .textTheme
                                        .titleLarge
                                        ?.fontSize, // استخدام حجم الخط من السمة
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                                letterSpacing: 0.5,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color:
                                    isDarkMode
                                        ? Colors.white10
                                        : theme.colorScheme.primary.withAlpha(
                                          15,
                                        ),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                'سورة الرعد: 28',
                                style: TextStyle(
                                  fontSize:
                                      Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.fontSize, // استخدام حجم الخط من السمة
                                  color:
                                      isDarkMode
                                          ? Colors.white70
                                          : theme.colorScheme.primary.withAlpha(
                                            200,
                                          ),
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // إحصائيات التسبيح الحالي
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildStatCard(
                              context,
                              'اليوم',
                              currentTasbih.todayCount,
                            ),
                            _buildStatCard(
                              context,
                              'الإجمالي',
                              currentTasbih.totalCount,
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // قائمة التسبيحات المحسنة
                      Container(
                        height: 80,
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          children: [
                            // عنوان القائمة
                            Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'التسبيحات',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color:
                                          isDarkMode
                                              ? Colors.white70
                                              : Colors.black87,
                                    ),
                                  ),
                                  Text(
                                    'اضغط مطولاً للتعديل',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color:
                                          isDarkMode
                                              ? Colors.white54
                                              : Colors.black54,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // قائمة التسبيحات
                            Expanded(
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: tasbihProvider.tasbihs.length,
                                itemBuilder: (context, index) {
                                  final tasbih = tasbihProvider.tasbihs[index];
                                  final isSelected =
                                      tasbih.id == currentTasbih.id;

                                  return GestureDetector(
                                    onTap: () {
                                      tasbihProvider.setCurrentTasbih(tasbih);
                                    },
                                    // إضافة خيار الضغط المطول لعرض قائمة التعديل أو الحذف
                                    onLongPress: () {
                                      _showTasbihOptionsDialog(context, tasbih);
                                    },
                                    child: AnimatedContainer(
                                      duration: const Duration(
                                        milliseconds: 300,
                                      ),
                                      margin: const EdgeInsets.only(right: 8),
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 8,
                                      ),
                                      decoration: BoxDecoration(
                                        color:
                                            isSelected
                                                ? theme.colorScheme.primary
                                                : isDarkMode
                                                ? Colors.black12
                                                : theme.colorScheme.primary
                                                    .withAlpha(20),
                                        borderRadius: BorderRadius.circular(30),
                                        border: Border.all(
                                          color:
                                              isSelected
                                                  ? theme.colorScheme.primary
                                                  : isDarkMode
                                                  ? Colors.white12
                                                  : theme.colorScheme.primary
                                                      .withAlpha(50),
                                          width: 1.5,
                                        ),
                                        boxShadow:
                                            isSelected
                                                ? [
                                                  BoxShadow(
                                                    color: theme
                                                        .colorScheme
                                                        .primary
                                                        .withAlpha(100),
                                                    blurRadius: 8,
                                                    spreadRadius: 1,
                                                    offset: const Offset(0, 2),
                                                  ),
                                                ]
                                                : null,
                                      ),
                                      child: Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              tasbih.text,
                                              style: TextStyle(
                                                color:
                                                    isSelected
                                                        ? theme
                                                            .colorScheme
                                                            .onPrimary
                                                        : theme
                                                            .colorScheme
                                                            .primary,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 14,
                                              ),
                                            ),
                                            if (isSelected)
                                              Container(
                                                margin: const EdgeInsets.only(
                                                  top: 4,
                                                ),
                                                width: 20,
                                                height: 3,
                                                decoration: BoxDecoration(
                                                  color:
                                                      theme
                                                          .colorScheme
                                                          .onPrimary,
                                                  borderRadius:
                                                      BorderRadius.circular(2),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20),

                      // نص التسبيحة الحالية
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 32),
                        child: Text(
                          currentTasbih.text,
                          style: TextStyle(
                            fontSize:
                                Theme.of(context)
                                    .textTheme
                                    .headlineSmall
                                    ?.fontSize, // استخدام حجم الخط من السمة
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 24),

                      // دائرة العداد
                      AbsorbPointer(
                        absorbing:
                            isCompleted, // تعطيل النقر عند الوصول إلى العدد المستهدف
                        child: GestureDetector(
                          onTap: () {
                            _playPressAnimation();

                            // إضافة اهتزاز عند الضغط
                            final feedbackProvider =
                                Provider.of<FeedbackProvider>(
                                  context,
                                  listen: false,
                                );
                            feedbackProvider.lightHapticFeedback();

                            tasbihProvider.incrementCount();
                          },
                          child: ScaleTransition(
                            scale: Tween<double>(
                              begin: 1.0,
                              end: 0.95,
                            ).animate(_animation),
                            child: Container(
                              width: 200,
                              height: 200,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color:
                                    isCompleted
                                        ? Colors
                                            .green // استخدام اللون الأخضر عند الاكتمال
                                        : theme.colorScheme.primary,
                                boxShadow: [
                                  BoxShadow(
                                    color:
                                        isCompleted
                                            ? Colors.green.withAlpha(75)
                                            : theme.colorScheme.primary
                                                .withAlpha(75),
                                    blurRadius: 10,
                                    spreadRadius: 2,
                                  ),
                                ],
                                // إضافة تأثير التدرج اللوني
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors:
                                      isCompleted
                                          ? [
                                            Colors.green, // لون أخضر فاتح
                                            Color.lerp(
                                              Colors.green,
                                              Colors.black,
                                              0.3,
                                            )!, // تدرج أغمق من اللون الأخضر
                                          ]
                                          : [
                                            theme
                                                .colorScheme
                                                .primary, // استخدام اللون الرئيسي
                                            Color.lerp(
                                              theme.colorScheme.primary,
                                              Colors.black,
                                              0.3,
                                            )!, // تدرج أغمق من اللون الرئيسي
                                          ],
                                ),
                              ),
                              child: Stack(
                                children: [
                                  // دائرة التقدم
                                  Center(
                                    child: SizedBox(
                                      width: 180,
                                      height: 180,
                                      child: CircularProgressIndicator(
                                        value: progress,
                                        backgroundColor: Colors.white.withAlpha(
                                          50,
                                        ),
                                        color: Colors.white,
                                        strokeWidth: 10,
                                      ),
                                    ),
                                  ),

                                  // العداد
                                  Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        if (isCompleted)
                                          AnimatedBuilder(
                                            animation: _congratsController,
                                            builder: (context, child) {
                                              // بدء الحركة الانتقالية عند اكتمال التسبيح مرة واحدة فقط
                                              if (!_isCongratsAnimationStarted) {
                                                // استخدام Future.microtask لتجنب setState أثناء البناء
                                                Future.microtask(() {
                                                  if (mounted) {
                                                    setState(() {
                                                      _isCongratsAnimationStarted =
                                                          true;
                                                    });
                                                    // تأكد من أن المتحكم جاهز قبل تشغيل الحركة
                                                    if (_congratsController
                                                            .isCompleted ||
                                                        _congratsController
                                                            .isDismissed) {
                                                      _congratsController
                                                          .reset();
                                                      _congratsController
                                                          .forward();
                                                    }
                                                  }
                                                });
                                              }

                                              // استخدام قيم آمنة للحركات الانتقالية
                                              final pulseValue =
                                                  _congratsPulseAnimation.value
                                                      .clamp(0.5, 1.5);
                                              final scaleValue =
                                                  _congratsScaleAnimation.value
                                                      .clamp(0.5, 1.5);
                                              final opacityValue =
                                                  _congratsOpacityAnimation
                                                      .value
                                                      .clamp(0.0, 1.0);

                                              return Column(
                                                children: [
                                                  // أيقونة الاكتمال مع حركة نبض
                                                  Transform.scale(
                                                    scale: pulseValue,
                                                    child: const Icon(
                                                      Icons.check_circle,
                                                      color: Colors.white,
                                                      size: 40,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 8),
                                                  // كلمة "أحسنت" مع حركات انتقالية محسنة
                                                  Transform.scale(
                                                    scale: scaleValue,
                                                    child: Opacity(
                                                      opacity: opacityValue,
                                                      child: Container(
                                                        padding:
                                                            const EdgeInsets.symmetric(
                                                              horizontal: 20,
                                                              vertical: 8,
                                                            ),
                                                        decoration: BoxDecoration(
                                                          color: Colors.white
                                                              .withAlpha(50),
                                                          borderRadius:
                                                              BorderRadius.circular(
                                                                20,
                                                              ),
                                                        ),
                                                        child: Text(
                                                          'أحسنت',
                                                          style: TextStyle(
                                                            fontSize: 40,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            color:
                                                                _congratsColorAnimation
                                                                    .value ??
                                                                Colors.white,
                                                            letterSpacing: 1.2,
                                                            shadows: [
                                                              const Shadow(
                                                                color:
                                                                    Colors
                                                                        .black38,
                                                                blurRadius: 6,
                                                                offset:
                                                                    Offset(
                                                                      0,
                                                                      2,
                                                                    ),
                                                              ),
                                                              Shadow(
                                                                color: Colors
                                                                    .white
                                                                    .withAlpha(
                                                                      150,
                                                                    ),
                                                                blurRadius: 12,
                                                                offset:
                                                                    const Offset(
                                                                      0,
                                                                      0,
                                                                    ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              );
                                            },
                                          ),
                                        if (!isCompleted)
                                          Text(
                                            '${currentTasbih.count}',
                                            style: const TextStyle(
                                              fontSize: 56,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white,
                                              shadows: [
                                                Shadow(
                                                  color: Colors.black26,
                                                  blurRadius: 4,
                                                  offset: Offset(0, 2),
                                                ),
                                              ],
                                            ),
                                          ),
                                        if (!isCompleted)
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 12,
                                              vertical: 2,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.white.withAlpha(50),
                                              borderRadius:
                                                  BorderRadius.circular(15),
                                            ),
                                            child: Text(
                                              '${currentTasbih.count} / ${currentTasbih.targetCount}',
                                              style: TextStyle(
                                                fontSize:
                                                    Theme.of(context)
                                                        .textTheme
                                                        .titleMedium
                                                        ?.fontSize, // استخدام حجم الخط من السمة
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // زر إعادة الضبط محسن
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        margin: const EdgeInsets.symmetric(horizontal: 50),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(30),
                          boxShadow: [
                            BoxShadow(
                              color:
                                  isCompleted
                                      ? Colors.green.withAlpha(
                                        76,
                                      ) // 0.3 opacity
                                      : theme.colorScheme.primary.withAlpha(
                                        51,
                                      ), // 0.2 opacity
                              blurRadius: isCompleted ? 8 : 4,
                              spreadRadius: isCompleted ? 2 : 1,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ElevatedButton.icon(
                          onPressed: () {
                            tasbihProvider.resetCount();
                            // إعادة ضبط حالة الحركة الانتقالية لكلمة "أحسنت"
                            setState(() {
                              _isCongratsAnimationStarted = false;
                            });
                            _congratsController.reset();

                            // إضافة اهتزاز عند إعادة الضبط
                            final feedbackProvider =
                                Provider.of<FeedbackProvider>(
                                  context,
                                  listen: false,
                                );
                            feedbackProvider.mediumHapticFeedback();

                            // إظهار رسالة تأكيد إعادة الضبط
                            feedbackProvider.showSuccessSnackBar(
                              context,
                              'تم إعادة ضبط العداد',
                              icon: Icons.refresh_rounded,
                            );
                          },
                          icon: AnimatedRotation(
                            duration: const Duration(milliseconds: 500),
                            turns: isCompleted ? 1 : 0,
                            child: Icon(
                              Icons.refresh_rounded,
                              size: 24,
                              color: isCompleted ? Colors.white : null,
                            ),
                          ),
                          label: const Text(
                            'إعادة ضبط',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                isCompleted
                                    ? Colors
                                        .green // استخدام اللون الأخضر عند الاكتمال
                                    : isDarkMode
                                    ? Colors.grey.shade800
                                    : Colors.grey.shade200,
                            foregroundColor:
                                isCompleted
                                    ? Colors
                                        .white // لون النص أبيض عند الاكتمال
                                    : theme.colorScheme.primary,
                            padding: const EdgeInsets.symmetric(
                              vertical: 15,
                              horizontal: 24,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(30),
                            ),
                            elevation:
                                0, // إزالة الارتفاع الافتراضي لاستخدام الظل المخصص
                          ),
                        ),
                      ),

                      // إضافة مساحة إضافية في الأسفل لتجنب تداخل المحتوى مع شريط التنقل
                      const SizedBox(height: 30),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatCard(BuildContext context, String title, int value) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      width: 130,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      decoration: BoxDecoration(
        color:
            isDarkMode
                ? Colors.black12
                : theme.colorScheme.primary.withAlpha(20),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              isDarkMode
                  ? Colors.white12
                  : theme.colorScheme.primary.withAlpha(40),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color:
                isDarkMode
                    ? Colors.black12
                    : theme.colorScheme.primary.withAlpha(10),
            blurRadius: 4,
            spreadRadius: 0.5,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize:
                  Theme.of(
                    context,
                  ).textTheme.bodyMedium?.fontSize, // استخدام حجم الخط من السمة
              fontWeight: FontWeight.w500,
              color: isDarkMode ? Colors.white70 : theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            value.toString(),
            style: TextStyle(
              fontSize:
                  Theme.of(context)
                      .textTheme
                      .headlineSmall
                      ?.fontSize, // استخدام حجم الخط من السمة
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  void _showAddTasbihDialog(BuildContext context) {
    final textController = TextEditingController();
    final countController = TextEditingController(text: '33');
    final theme = Theme.of(context);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
              left: 16,
              right: 16,
              top: 16,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text(
                  'إضافة تسبيحة جديدة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: textController,
                  decoration: InputDecoration(
                    labelText: 'نص التسبيحة *',
                    border: const OutlineInputBorder(),
                    hintText: 'أدخل نص التسبيحة',
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: theme.colorScheme.primary,
                        width: 2,
                      ),
                    ),
                  ),
                  textDirection: TextDirection.rtl,
                  textAlign: TextAlign.right,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: countController,
                  decoration: InputDecoration(
                    labelText: 'عدد التكرار * (الحد الأقصى: 200)',
                    border: const OutlineInputBorder(),
                    hintText: 'عدد مرات التكرار (1-200)',
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: theme.colorScheme.primary,
                        width: 2,
                      ),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        if (textController.text.trim().isEmpty) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('يرجى إدخال نص التسبيحة'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        final count = int.tryParse(countController.text);
                        if (count == null || count < 1) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('يرجى إدخال عدد تكرار صحيح'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        if (count > 200) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('الحد الأقصى لعدد التكرار هو 200'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        Provider.of<TasbihProvider>(
                          context,
                          listen: false,
                        ).addTasbih(textController.text, count);

                        Navigator.pop(context);

                        // إظهار رسالة نجاح
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تمت إضافة التسبيحة بنجاح'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('إضافة'),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
    );
  }

  void _showSettingsDialog(BuildContext context) {
    final theme = Theme.of(context);

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Consumer<TasbihProvider>(
            builder: (context, tasbihProvider, child) {
              return Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const Text(
                      'إعدادات المسبحة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),

                    SwitchListTile(
                      title: const Text('تفعيل الاهتزاز'),
                      value: tasbihProvider.vibrationEnabled,
                      onChanged: (value) {
                        tasbihProvider.setVibrationEnabled(value);
                      },
                      activeColor: theme.colorScheme.primary,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('إغلاق'),
                    ),
                  ],
                ),
              );
            },
          ),
    );
  }

  // دالة لعرض خيارات التعديل أو الحذف للتسبيحة

  void _showTasbihOptionsDialog(BuildContext context, Tasbih tasbih) {
    final theme = Theme.of(context);

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                tasbih.text,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              // زر تعديل التسبيحة
              ListTile(
                leading: Icon(Icons.edit, color: theme.colorScheme.primary),
                title: const Text('تعديل التسبيحة'),
                onTap: () {
                  Navigator.pop(context);
                  _showEditTasbihDialog(context, tasbih);
                },
              ),

              // زر حذف التسبيحة
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('حذف التسبيحة'),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteConfirmationDialog(context, tasbih);
                },
              ),

              const SizedBox(height: 16),

              // زر إلغاء
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          ),
        );
      },
    );
  }

  // دالة لعرض مربع حوار تعديل التسبيحة
  void _showEditTasbihDialog(BuildContext context, Tasbih tasbih) {
    final textController = TextEditingController(text: tasbih.text);
    final countController = TextEditingController(
      text: tasbih.targetCount.toString(),
    );
    final theme = Theme.of(context);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
              left: 16,
              right: 16,
              top: 16,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text(
                  'تعديل التسبيحة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: textController,
                  decoration: const InputDecoration(
                    labelText: 'نص التسبيحة *',
                    border: OutlineInputBorder(),
                    hintText: 'أدخل نص التسبيحة',
                  ),
                  textDirection: TextDirection.rtl,
                  textAlign: TextAlign.right,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: countController,
                  decoration: const InputDecoration(
                    labelText: 'عدد التكرار * (الحد الأقصى: 200)',
                    border: OutlineInputBorder(),
                    hintText: 'عدد مرات التكرار (1-200)',
                  ),
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        if (textController.text.trim().isEmpty) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('يرجى إدخال نص التسبيحة'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        final count = int.tryParse(countController.text);
                        if (count == null || count < 1) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('يرجى إدخال عدد تكرار صحيح'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        if (count > 200) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('الحد الأقصى لعدد التكرار هو 200'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        // تحديث التسبيحة
                        final updatedTasbih = tasbih.copyWith(
                          text: textController.text,
                          targetCount: count,
                        );

                        Provider.of<TasbihProvider>(
                          context,
                          listen: false,
                        ).updateTasbih(updatedTasbih);

                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('حفظ التعديلات'),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
    );
  }

  // دالة لعرض مربع حوار تأكيد الحذف
  void _showDeleteConfirmationDialog(BuildContext context, Tasbih tasbih) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text('هل أنت متأكد من حذف التسبيحة "${tasbih.text}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Provider.of<TasbihProvider>(
                    context,
                    listen: false,
                  ).deleteTasbih(tasbih.id);
                  Navigator.pop(context);

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف التسبيحة بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }
}
