import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../services/prayer_provider.dart';
import '../screens/prayer_times_screen.dart';
import '../widgets/page_transitions.dart';

/// شريط أوقات الصلاة المميز
class PrayerTimesBar extends StatelessWidget {
  const PrayerTimesBar({super.key});

  /// الحصول على أيقونة الصلاة المناسبة
  IconData _getPrayerIcon(String prayerName) {
    switch (prayerName) {
      case 'الفجر':
        return Icons.wb_twilight; // أيقونة الفجر
      case 'الشروق':
        return Icons.wb_sunny; // أيقونة الشروق
      case 'الظهر':
        return Icons.wb_sunny_outlined; // أيقونة الظهر
      case 'العصر':
        return Icons.wb_incandescent; // أيقونة العصر
      case 'المغرب':
        return Icons.nights_stay; // أيقونة المغرب
      case 'العشاء':
        return Icons.bedtime; // أيقونة العشاء
      default:
        return Icons.access_time; // أيقونة افتراضية
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Consumer<PrayerProvider>(
      builder: (context, prayerProvider, child) {
        if (prayerProvider.isLoading) {
          return _buildLoadingBar(theme);
        }

        if (prayerProvider.errorMessage.isNotEmpty) {
          return _buildErrorBar(theme, prayerProvider.errorMessage);
        }

        return _buildPrayerBar(theme, prayerProvider, context);
      },
    );
  }

  /// بناء شريط التحميل المصغر
  Widget _buildLoadingBar(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary.withValues(alpha: 0.1),
            theme.colorScheme.primary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 18,
            height: 18,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                theme.colorScheme.primary,
              ),
            ),
          ),
          const SizedBox(width: 10),
          Text(
            'جاري تحميل أوقات الصلاة...',
            style: TextStyle(
              fontSize: 13,
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط الخطأ المصغر
  Widget _buildErrorBar(ThemeData theme, String error) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 18),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              'خطأ في تحميل أوقات الصلاة',
              style: TextStyle(
                fontSize: 13,
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط أوقات الصلاة المصغر
  Widget _buildPrayerBar(
    ThemeData theme,
    PrayerProvider prayerProvider,
    BuildContext context,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      height: 60, // تحديد ارتفاع ثابت أصغر
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withValues(alpha: 0.2),
            blurRadius: 6,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            HapticFeedback.lightImpact();
            Navigator.push(
              context,
              PageTransition(
                type: PageTransitionType.rightToLeftWithFade,
                child: const PrayerTimesScreen(),
                duration: const Duration(milliseconds: 400),
                curve: Curves.easeInOut,
              ),
            );
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              children: [
                // أيقونة الصلاة المناسبة (مصغرة)
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getPrayerIcon(prayerProvider.nextPrayerName),
                    color: Colors.white,
                    size: 18,
                  ),
                ),
                const SizedBox(width: 12),

                // معلومات الصلاة (مضغوطة)
                Expanded(
                  child: Row(
                    children: [
                      // الصلاة القادمة
                      Text(
                        prayerProvider.nextPrayerName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),

                      // أيقونة المؤقت
                      const Icon(Icons.timer, color: Colors.white70, size: 14),
                      const SizedBox(width: 4),

                      // العد التنازلي
                      Text(
                        prayerProvider.getFormattedRemainingTime(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ],
                  ),
                ),

                // سهم للانتقال (مصغر)
                Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white,
                    size: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
