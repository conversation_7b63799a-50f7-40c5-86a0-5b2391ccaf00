import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/statistics_model.dart';
import '../models/achievement_model.dart';
import '../utils/logger.dart';

/// خدمة إدارة الإحصائيات والإنجازات
class StatisticsService {
  static final StatisticsService _instance = StatisticsService._internal();
  factory StatisticsService() => _instance;
  StatisticsService._internal();

  Database? _database;
  static const String _databaseName = 'statistics.db';
  static const int _databaseVersion = 1;

  /// الحصول على قاعدة البيانات
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    final path = join(await getDatabasesPath(), _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
    );
  }

  /// إنشاء جداول قاعدة البيانات
  Future<void> _onCreate(Database db, int version) async {
    try {
      // جدول الإحصائيات اليومية
      await db.execute('''
        CREATE TABLE daily_statistics(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          date TEXT UNIQUE NOT NULL,
          completedAzkar INTEGER DEFAULT 0,
          totalAzkar INTEGER DEFAULT 0,
          completedCategories INTEGER DEFAULT 0,
          totalCategories INTEGER DEFAULT 0,
          points INTEGER DEFAULT 0,
          timeSpent INTEGER DEFAULT 0
        )
      ''');

      // جدول الإنجازات
      await db.execute('''
        CREATE TABLE achievements(
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          description TEXT NOT NULL,
          iconCodePoint INTEGER NOT NULL,
          type INTEGER NOT NULL,
          level INTEGER NOT NULL,
          targetValue INTEGER NOT NULL,
          currentValue INTEGER DEFAULT 0,
          points INTEGER NOT NULL,
          isUnlocked INTEGER DEFAULT 0,
          unlockedDate TEXT,
          colorValue INTEGER NOT NULL
        )
      ''');

      // جدول بيانات السلسلة المتتالية
      await db.execute('''
        CREATE TABLE streak_data(
          id INTEGER PRIMARY KEY,
          currentStreak INTEGER DEFAULT 0,
          longestStreak INTEGER DEFAULT 0,
          lastActiveDate TEXT,
          streakStartDate TEXT
        )
      ''');

      AppLogger.info('تم إنشاء جداول الإحصائيات بنجاح');

      // إدخال الإنجازات الافتراضية
      await _insertDefaultAchievements(db);

      // إدخال بيانات السلسلة الافتراضية
      await db.insert('streak_data', {
        'id': 1,
        'currentStreak': 0,
        'longestStreak': 0,
      });
    } catch (e) {
      AppLogger.error('خطأ في إنشاء جداول الإحصائيات: $e');
      rethrow;
    }
  }

  /// إدخال الإنجازات الافتراضية
  Future<void> _insertDefaultAchievements(Database db) async {
    // استخدام الإنجازات من نموذج Achievement
    final achievements = [
      Achievement(
        id: 'streak_3',
        title: 'البداية المباركة',
        description: 'أكمل الأذكار لمدة 3 أيام متتالية',
        icon: Icons.local_fire_department,
        type: AchievementType.streak,
        level: AchievementLevel.bronze,
        targetValue: 3,
        currentValue: 0,
        points: 50,
        isUnlocked: false,
        color: const Color(0xFFCD7F32),
      ),
      Achievement(
        id: 'daily_complete',
        title: 'يوم مبارك',
        description: 'أكمل جميع الأذكار في يوم واحد',
        icon: Icons.check_circle,
        type: AchievementType.daily,
        level: AchievementLevel.bronze,
        targetValue: 1,
        currentValue: 0,
        points: 25,
        isUnlocked: false,
        color: const Color(0xFFCD7F32),
      ),
    ];

    for (final achievement in achievements) {
      await db.insert('achievements', achievement.toMap());
    }
    AppLogger.info('تم إدخال الإنجازات الافتراضية');
  }

  /// حفظ الإحصائيات اليومية
  Future<void> saveDailyStatistics(DailyStatistics stats) async {
    try {
      final db = await database;
      await db.insert(
        'daily_statistics',
        stats.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      AppLogger.info('تم حفظ الإحصائيات اليومية: ${stats.date}');
    } catch (e) {
      AppLogger.error('خطأ في حفظ الإحصائيات اليومية: $e');
    }
  }

  /// الحصول على الإحصائيات اليومية
  Future<DailyStatistics?> getDailyStatistics(DateTime date) async {
    try {
      final db = await database;
      final dateString = date.toIso8601String().split('T')[0];

      final result = await db.query(
        'daily_statistics',
        where: 'date LIKE ?',
        whereArgs: ['$dateString%'],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return DailyStatistics.fromMap(result.first);
      }
      return null;
    } catch (e) {
      AppLogger.error('خطأ في استرجاع الإحصائيات اليومية: $e');
      return null;
    }
  }

  /// الحصول على الإحصائيات الأسبوعية
  Future<WeeklyStatistics> getWeeklyStatistics(DateTime weekStart) async {
    try {
      final db = await database;
      final weekEnd = weekStart.add(const Duration(days: 6));

      final result = await db.query(
        'daily_statistics',
        where: 'date >= ? AND date <= ?',
        whereArgs: [
          weekStart.toIso8601String().split('T')[0],
          weekEnd.toIso8601String().split('T')[0],
        ],
        orderBy: 'date ASC',
      );

      final dailyStats =
          result.map((map) => DailyStatistics.fromMap(map)).toList();
      final totalPoints = dailyStats.fold(0, (sum, stat) => sum + stat.points);
      final streakData = await getStreakData();

      return WeeklyStatistics(
        weekStart: weekStart,
        dailyStats: dailyStats,
        totalPoints: totalPoints,
        streakDays: streakData.currentStreak,
      );
    } catch (e) {
      AppLogger.error('خطأ في استرجاع الإحصائيات الأسبوعية: $e');
      return WeeklyStatistics(
        weekStart: weekStart,
        dailyStats: [],
        totalPoints: 0,
        streakDays: 0,
      );
    }
  }

  /// الحصول على الإحصائيات الشهرية
  Future<MonthlyStatistics> getMonthlyStatistics(DateTime month) async {
    try {
      final db = await database;
      final monthStart = DateTime(month.year, month.month, 1);
      final monthEnd = DateTime(month.year, month.month + 1, 0);

      final result = await db.query(
        'daily_statistics',
        where: 'date >= ? AND date <= ?',
        whereArgs: [
          monthStart.toIso8601String().split('T')[0],
          monthEnd.toIso8601String().split('T')[0],
        ],
        orderBy: 'date ASC',
      );

      final dailyStats =
          result.map((map) => DailyStatistics.fromMap(map)).toList();
      final totalPoints = dailyStats.fold(0, (sum, stat) => sum + stat.points);
      final streakData = await getStreakData();

      return MonthlyStatistics(
        month: month,
        dailyStats: dailyStats,
        totalPoints: totalPoints,
        longestStreak: streakData.longestStreak,
        currentStreak: streakData.currentStreak,
      );
    } catch (e) {
      AppLogger.error('خطأ في استرجاع الإحصائيات الشهرية: $e');
      return MonthlyStatistics(
        month: month,
        dailyStats: [],
        totalPoints: 0,
        longestStreak: 0,
        currentStreak: 0,
      );
    }
  }

  /// الحصول على بيانات السلسلة المتتالية
  Future<StreakData> getStreakData() async {
    try {
      final db = await database;
      final result =
          await db.query('streak_data', where: 'id = ?', whereArgs: [1]);

      if (result.isNotEmpty) {
        return StreakData.fromMap(result.first);
      }

      // إنشاء بيانات افتراضية إذا لم توجد
      return StreakData(currentStreak: 0, longestStreak: 0);
    } catch (e) {
      AppLogger.error('خطأ في استرجاع بيانات السلسلة: $e');
      return StreakData(currentStreak: 0, longestStreak: 0);
    }
  }

  /// تحديث بيانات السلسلة المتتالية
  Future<void> updateStreakData(StreakData streakData) async {
    try {
      final db = await database;
      await db.update(
        'streak_data',
        streakData.toMap(),
        where: 'id = ?',
        whereArgs: [1],
      );
      AppLogger.info('تم تحديث بيانات السلسلة المتتالية');
    } catch (e) {
      AppLogger.error('خطأ في تحديث بيانات السلسلة: $e');
    }
  }

  /// الحصول على جميع الإنجازات
  Future<List<Achievement>> getAllAchievements() async {
    try {
      final db = await database;
      final result =
          await db.query('achievements', orderBy: 'level ASC, points ASC');
      return result.map((map) => Achievement.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('خطأ في استرجاع الإنجازات: $e');
      return [];
    }
  }

  /// تحديث إنجاز
  Future<void> updateAchievement(Achievement achievement) async {
    try {
      final db = await database;
      await db.update(
        'achievements',
        achievement.toMap(),
        where: 'id = ?',
        whereArgs: [achievement.id],
      );
      AppLogger.info('تم تحديث الإنجاز: ${achievement.title}');
    } catch (e) {
      AppLogger.error('خطأ في تحديث الإنجاز: $e');
    }
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
