import 'dart:async';
import 'package:flutter/material.dart';
import 'package:azkary/screens/main_screen.dart';
import 'package:azkary/widgets/app_logo.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  // متغيرات مبسطة للرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // تهيئة مبسطة للرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800), // مدة أقصر
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    // بدء الرسوم المتحركة
    _animationController.forward();

    // انتقال مباشر وسريع للشاشة الرئيسية
    Timer(const Duration(milliseconds: 1200), () {
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const MainScreen(showDailyAyah: true),
          ),
        );
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // ألوان مبسطة
    final backgroundColor = isDarkMode ? const Color(0xFF121212) : Colors.white;
    final textColor = isDarkMode ? Colors.white : const Color(0xFF2E7D32);

    return Scaffold(
      backgroundColor: backgroundColor,
      body: Center(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // الشعار مبسط
              AppLogo(size: 120), // حجم أصغر للدقة 1080p

              const SizedBox(height: 24),

              // نص الترحيب مبسط
              Text(
                'أذكاري',
                style: TextStyle(
                  color: textColor,
                  fontSize: 24, // حجم أصغر للدقة 1080p
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'تطبيق الأذكار اليومية',
                style: TextStyle(
                  color: textColor.withAlpha(180),
                  fontSize: 14, // حجم أصغر للدقة 1080p
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
