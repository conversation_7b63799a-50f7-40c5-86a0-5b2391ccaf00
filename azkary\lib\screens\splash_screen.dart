import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:azkary/screens/main_screen.dart';
import 'package:azkary/widgets/app_logo.dart';
import 'package:azkary/widgets/islamic_pattern.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  // متغيرات للرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _rotateAnimation;

  // متغيرات للنص
  late Animation<double> _textFadeAnimation;

  // متغير للتحقق من اكتمال التحميل
  bool _isLoading = true;
  double _loadingProgress = 0.0;

  @override
  void initState() {
    super.initState();

    // تهيئة وحدة التحكم بالرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2500),
    );

    // تهيئة الرسوم المتحركة للشعار
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOutBack),
      ),
    );

    _slideAnimation = Tween<double>(begin: -50.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
      ),
    );

    _rotateAnimation = Tween<double>(begin: 0.0, end: 0.05).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.5, 0.7, curve: Curves.easeInOut),
      ),
    );

    // تهيئة الرسوم المتحركة للنص
    _textFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.6, 0.8, curve: Curves.easeIn),
      ),
    );

    // بدء الرسوم المتحركة
    _animationController.forward();

    // محاكاة عملية التحميل
    _simulateLoading();

    // الانتقال إلى الشاشة الرئيسية بعد انتهاء الرسوم المتحركة
    Timer(const Duration(milliseconds: 3500), () {
      // الانتقال إلى الشاشة الرئيسية
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder:
              (context, animation, secondaryAnimation) =>
                  const MainScreen(showDailyAyah: true),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOut;

            var tween = Tween(
              begin: begin,
              end: end,
            ).chain(CurveTween(curve: curve));
            var offsetAnimation = animation.drive(tween);

            return SlideTransition(
              position: offsetAnimation,
              child: FadeTransition(opacity: animation, child: child),
            );
          },
          transitionDuration: const Duration(milliseconds: 800),
        ),
      );
    });
  }

  // محاكاة عملية التحميل بطريقة أكثر كفاءة
  void _simulateLoading() {
    // استخدام مؤقت واحد بدلاً من عدة مؤقتات
    Future.delayed(const Duration(milliseconds: 100), () {
      if (!mounted) return;

      setState(() {
        _loadingProgress = 0.2;
      });

      Future.delayed(const Duration(milliseconds: 500), () {
        if (!mounted) return;

        setState(() {
          _loadingProgress = 0.5;
        });

        Future.delayed(const Duration(milliseconds: 1000), () {
          if (!mounted) return;

          setState(() {
            _loadingProgress = 0.8;
          });

          Future.delayed(const Duration(milliseconds: 900), () {
            if (!mounted) return;

            setState(() {
              _loadingProgress = 1.0;
              _isLoading = false;
            });
          });
        });
      });
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تعيين وضع الشاشة للعرض بشكل طبيعي مع إظهار شريط الإشعارات
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // ألوان التطبيق
    const primaryColor = Color(
      0xFF2E7D32,
    ); // اللون الأخضر الرئيسي للتطبيق
    final backgroundColor = isDarkMode ? const Color(0xFF121212) : Colors.white;
    final textColor = isDarkMode ? Colors.white : const Color(0xFF2E7D32);

    return Scaffold(
      backgroundColor: backgroundColor,
      body: Stack(
        children: [
          // زخارف إسلامية في الخلفية
          Positioned.fill(
            child: Opacity(
              opacity: isDarkMode ? 0.05 : 0.1,
              child: IslamicPattern(
                color: primaryColor,
                opacity: isDarkMode ? 0.1 : 0.2,
              ),
            ),
          ),

          // محتوى الشاشة
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // الشعار مع الرسوم المتحركة
                AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return Transform.translate(
                      offset: Offset(0, _slideAnimation.value),
                      child: Transform.rotate(
                        angle: _rotateAnimation.value,
                        child: Transform.scale(
                          scale: _scaleAnimation.value,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: AppLogo(size: 150),
                          ),
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 30),

                // نص الترحيب
                FadeTransition(
                  opacity: _textFadeAnimation,
                  child: Column(
                    children: [
                      Text(
                        'أذكاري',
                        style: TextStyle(
                          color: textColor,
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        'تطبيق الأذكار اليومية',
                        style: TextStyle(
                          color: textColor.withAlpha(204), // 0.8 * 255 = 204
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 50),

                // شريط التقدم
                if (_isLoading)
                  FadeTransition(
                    opacity: _textFadeAnimation,
                    child: Column(
                      children: [
                        SizedBox(
                          width: 200,
                          child: LinearProgressIndicator(
                            value: _loadingProgress,
                            backgroundColor: Colors.grey.withAlpha(
                              51,
                            ), // 0.2 * 255 = 51
                            valueColor: AlwaysStoppedAnimation<Color>(
                              primaryColor,
                            ),
                            borderRadius: BorderRadius.circular(10),
                            minHeight: 6,
                          ),
                        ),
                        const SizedBox(height: 10),
                        Text(
                          'جاري التحميل... ${(_loadingProgress * 100).toInt()}%',
                          style: TextStyle(
                            color: textColor.withAlpha(153), // 0.6 * 255 = 153
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
