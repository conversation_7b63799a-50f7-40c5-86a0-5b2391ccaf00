import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import '../models/azkar_model.dart';
import '../utils/logger.dart';

class CustomAzkarService {
  static final CustomAzkarService _instance = CustomAzkarService._internal();
  factory CustomAzkarService() => _instance;
  CustomAzkarService._internal() {
    // Initialize FFI for desktop platforms
    _initializePlatformSpecific();
  }

  // تهيئة خاصة بالمنصة
  void _initializePlatformSpecific() {
    try {
      // لا نحتاج إلى تهيئة databaseFactory هنا
      // لأننا قمنا بتهيئته بالفعل في main.dart
      AppLogger.info('تم تهيئة قاعدة البيانات مسبقاً في main.dart');
    } catch (e) {
      AppLogger.error('خطأ في _initializePlatformSpecific: $e');
    }
  }

  static Database? _database;

  // الحصول على قاعدة البيانات
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    final path = join(await getDatabasesPath(), 'custom_azkar.db');
    return await openDatabase(path, version: 1, onCreate: _onCreate);
  }

  // إنشاء جدول الأذكار الخاصة
  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE custom_azkar(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        text TEXT NOT NULL,
        count INTEGER NOT NULL,
        fadl TEXT,
        isFavorite INTEGER DEFAULT 0,
        currentCount INTEGER DEFAULT 0,
        createdAt TEXT NOT NULL
      )
    ''');
    AppLogger.info('تم إنشاء جدول الأذكار الخاصة بنجاح');
  }

  // إضافة ذكر خاص جديد
  Future<int> addCustomZikr(CustomZikr zikr) async {
    try {
      final db = await database;
      AppLogger.info('جاري إضافة ذكر خاص جديد: ${zikr.text}');
      final id = await db.insert('custom_azkar', zikr.toMap());
      AppLogger.info('تم إضافة الذكر الخاص بنجاح، المعرف: $id');
      return id;
    } catch (e) {
      AppLogger.error('خطأ في إضافة الذكر الخاص: $e');
      // محاولة إعادة إنشاء الجدول
      await _recreateTable();
      try {
        final db = await database;
        final id = await db.insert('custom_azkar', zikr.toMap());
        return id;
      } catch (e) {
        AppLogger.error(
          'فشل في إضافة الذكر الخاص حتى بعد إعادة إنشاء الجدول: $e',
        );
        return -1;
      }
    }
  }

  // الحصول على جميع الأذكار الخاصة
  Future<List<CustomZikr>> getCustomAzkar() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'custom_azkar',
        orderBy: 'createdAt DESC',
      );
      AppLogger.info('تم العثور على ${maps.length} ذكر خاص');
      return List.generate(maps.length, (i) => CustomZikr.fromMap(maps[i]));
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الأذكار الخاصة: $e');
      // محاولة إعادة إنشاء الجدول
      await _recreateTable();
      return [];
    }
  }

  // تحديث ذكر خاص
  Future<bool> updateCustomZikr(CustomZikr zikr) async {
    try {
      final db = await database;
      final count = await db.update(
        'custom_azkar',
        zikr.toMap(),
        where: 'id = ?',
        whereArgs: [zikr.id],
      );
      return count > 0;
    } catch (e) {
      AppLogger.error('خطأ في تحديث الذكر الخاص: $e');
      return false;
    }
  }

  // حذف ذكر خاص
  Future<bool> deleteCustomZikr(int id) async {
    try {
      final db = await database;
      final count = await db.delete(
        'custom_azkar',
        where: 'id = ?',
        whereArgs: [id],
      );
      return count > 0;
    } catch (e) {
      AppLogger.error('خطأ في حذف الذكر الخاص: $e');
      return false;
    }
  }

  // إعادة إنشاء جدول الأذكار الخاصة
  Future<void> _recreateTable() async {
    try {
      AppLogger.warning('جاري إعادة إنشاء جدول الأذكار الخاصة...');
      final db = await database;
      await db.execute('DROP TABLE IF EXISTS custom_azkar');
      await db.execute('''
        CREATE TABLE custom_azkar(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          text TEXT NOT NULL,
          count INTEGER NOT NULL,
          fadl TEXT,
          isFavorite INTEGER DEFAULT 0,
          currentCount INTEGER DEFAULT 0,
          createdAt TEXT NOT NULL
        )
      ''');
      AppLogger.info('تم إعادة إنشاء جدول الأذكار الخاصة بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في إعادة إنشاء جدول الأذكار الخاصة: $e');
    }
  }
}
