import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/azkar_model.dart';
import '../models/azkar_completion_model.dart';
import '../constants/azkar_data.dart';
import '../utils/logger.dart';
import 'database_service.dart';
import 'local_storage_service.dart';
import 'statistics_provider.dart';
import 'comprehensive_statistics_service.dart';
import 'azkar_settings_service.dart';
import '../widgets/completion_celebration_dialog.dart';

class AzkarProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final LocalStorageService _localStorageService = LocalStorageService();
  final ComprehensiveStatisticsService _comprehensiveStatsService =
      ComprehensiveStatisticsService();
  StatisticsProvider? _statisticsProvider;
  List<Category> _categories = [];
  List<Zikr> _currentAzkar = [];
  List<Zikr> _favoriteAzkar = [];
  List<Zikr> _customAzkar = []; // قائمة الأذكار الخاصة
  Zikr? _dailyZikr;
  bool _isDarkMode = false;
  String _currentCategory = '';
  bool _isLoading = true;

  // متغيرات إدارة إكمال الأذكار
  final Map<String, AzkarCompletionModel> _azkarCompletionStatus = {};
  final StreamController<AzkarCompletionModel> _completionController =
      StreamController<AzkarCompletionModel>.broadcast();
  bool _isCompletionAnimationActive = false;

  @override
  void dispose() {
    _completionController.close();
    super.dispose();
  }

  List<Category> get categories => _categories;
  List<Zikr> get currentAzkar => _currentAzkar;
  List<Zikr> get favoriteAzkar => _favoriteAzkar;
  List<Zikr> get customAzkar => _customAzkar;
  Zikr? get dailyZikr => _dailyZikr;
  bool get isDarkMode => _isDarkMode;
  String get currentCategory => _currentCategory;
  bool get isLoading => _isLoading;

  /// الحصول على الأذكار مع تطبيق إعدادات الإخفاء
  Future<List<Zikr>> getFilteredCurrentAzkar() async {
    final hideCompleted = await AzkarSettingsService.getHideCompletedAzkar();
    if (hideCompleted) {
      return _currentAzkar
          .where((zikr) => zikr.currentCount < zikr.count)
          .toList();
    }
    return _currentAzkar;
  }

  /// الحصول على الأذكار المخصصة مع تطبيق إعدادات الإخفاء
  Future<List<Zikr>> getFilteredCustomAzkar() async {
    final hideCompleted = await AzkarSettingsService.getHideCompletedAzkar();
    if (hideCompleted) {
      return _customAzkar
          .where((zikr) => zikr.currentCount < zikr.count)
          .toList();
    }
    return _customAzkar;
  }

  // دوال إدارة إكمال الأذكار
  Map<String, AzkarCompletionModel> get azkarCompletionStatus =>
      _azkarCompletionStatus;
  Stream<AzkarCompletionModel> get onAzkarCategoryCompleted =>
      _completionController.stream;
  bool get isCompletionAnimationActive => _isCompletionAnimationActive;

  AzkarProvider() {
    _initializeApp();
  }

  /// تعيين مزود الإحصائيات
  void setStatisticsProvider(StatisticsProvider statisticsProvider) {
    _statisticsProvider = statisticsProvider;
  }

  Future<void> _initializeApp() async {
    _isLoading = true;
    notifyListeners();

    try {
      // تحميل الإعدادات أولاً
      await _loadThemePreference();

      // تحميل التصنيفات - هذه خطوة مهمة جداً
      await _loadCategories();

      // التحقق من عدد التصنيفات;

      // إذا كانت التصنيفات فارغة، حاول إعادة تحميلها
      if (_categories.isEmpty) {
        // إعادة تهيئة قاعدة البيانات
        bool reinitialized =
            await _databaseService.checkAndReinitializeDatabase();
        if (reinitialized) {
          // إعادة تحميل التصنيفات بعد إعادة التهيئة
          await _loadCategories();
        }
      }

      // تحميل المفضلة بعد تحميل التصنيفات
      await _loadFavoriteAzkar();

      // تحميل الأذكار الخاصة
      await _loadCustomAzkar();

      // تحميل ذكر اليوم بعد تحميل التصنيفات
      if (_categories.isNotEmpty) {
        await _loadDailyZikr();
      }
    } catch (e) {
      // تحميل البيانات الأساسية على الأقل
      if (_categories.isEmpty) {
        // محاولة إعادة تهيئة قاعدة البيانات وتحميل التصنيفات مرة أخرى
        await _databaseService.checkAndReinitializeDatabase();
        await _loadCategories();
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _loadThemePreference() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    _isDarkMode = prefs.getBool('dark_mode') ?? false;
    notifyListeners();
  }

  Future<void> toggleTheme() async {
    _isDarkMode = !_isDarkMode;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool('dark_mode', _isDarkMode);
    notifyListeners();
  }

  Future<void> _loadCategories() async {
    try {
      AppLogger.info('بدء تحميل التصنيفات...');

      // تحميل التصنيفات من قاعدة البيانات
      _categories = await _databaseService.getCategories();
      AppLogger.info('تم تحميل ${_categories.length} تصنيف من قاعدة البيانات');

      // التحقق من وجود التصنيفات
      if (_categories.isEmpty) {
        AppLogger.warning(
          'لا توجد تصنيفات في قاعدة البيانات، جاري إعادة التهيئة...',
        );

        // إذا كانت التصنيفات فارغة، قم بإعادة تهيئة قاعدة البيانات
        await _databaseService.checkAndReinitializeDatabase();
        _categories = await _databaseService.getCategories();
        AppLogger.info('بعد إعادة التهيئة: ${_categories.length} تصنيف');

        // إذا ظلت التصنيفات فارغة، استخدم البيانات المحلية مباشرة
        if (_categories.isEmpty) {
          AppLogger.warning(
            'لا تزال التصنيفات فارغة، جاري استخدام البيانات المحلية...',
          );
          _categories = List.from(categoriesData);
          AppLogger.info(
            'تم تحميل ${_categories.length} تصنيف من البيانات المحلية',
          );

          // حفظ البيانات المحلية في قاعدة البيانات
          await _forceUseLocalData();
        }
      }

      notifyListeners();
    } catch (e) {
      AppLogger.error('خطأ في تحميل التصنيفات: $e');

      // في حالة الفشل، استخدم البيانات المحلية مباشرة
      if (_categories.isEmpty) {
        AppLogger.info('جاري استخدام البيانات المحلية بسبب الخطأ...');
        _categories = List.from(categoriesData);
        AppLogger.info(
          'تم تحميل ${_categories.length} تصنيف من البيانات المحلية',
        );

        // حفظ البيانات المحلية في قاعدة البيانات
        await _forceUseLocalData();

        notifyListeners();
      }
    }
  }

  // دالة لإدخال البيانات المحلية في قاعدة البيانات بشكل مباشر
  Future<void> _forceUseLocalData() async {
    try {
      AppLogger.info('جاري إدخال البيانات المحلية في قاعدة البيانات...');

      // إعادة تهيئة قاعدة البيانات
      await _databaseService.checkAndReinitializeDatabase();

      // التحقق من نجاح الإدخال
      final categories = await _databaseService.getCategories();
      AppLogger.info('تم إدخال ${categories.length} تصنيف في قاعدة البيانات');
    } catch (e) {
      AppLogger.error('خطأ في إدخال البيانات المحلية في قاعدة البيانات: $e');
    }
  }

  // دالة عامة لإعادة تحميل البيانات
  Future<void> reloadData() async {
    _isLoading = true;
    notifyListeners();

    try {
      AppLogger.info('بدء إعادة تحميل البيانات...');

      // تحميل التصنيفات من قاعدة البيانات
      AppLogger.info('جاري تحميل التصنيفات...');
      _categories = await _databaseService.getCategories();
      AppLogger.info('عدد التصنيفات بعد التحميل: ${_categories.length}');

      // طباعة معلومات التصنيفات للتأكد من تحميلها بشكل صحيح
      for (var category in _categories) {
        AppLogger.info(
          'التصنيف: ${category.name}, المعرف: ${category.id}, الأيقونة: ${category.icon}, العدد: ${category.count}',
        );
      }

      // تحميل الأذكار المفضلة
      AppLogger.info('جاري تحميل الأذكار المفضلة...');
      await _loadFavoriteAzkar();

      // تحميل الأذكار الحالية إذا كان هناك تصنيف محدد
      if (_currentCategory.isNotEmpty) {
        AppLogger.info('جاري تحميل الأذكار للتصنيف: $_currentCategory');
        _currentAzkar = await _databaseService.getAzkarByCategory(
          _currentCategory,
        );
        AppLogger.info(
          'تم تحميل ${_currentAzkar.length} ذكر للتصنيف: $_currentCategory',
        );
      }

      // تحميل ذكر اليوم
      if (_categories.isNotEmpty) {
        AppLogger.info('جاري تحميل ذكر اليوم...');
        await _loadDailyZikr();
      }

      // إعادة ترتيب التصنيفات حسب المعرف
      _categories.sort((a, b) => a.id.compareTo(b.id));

      AppLogger.info('تم إعادة تحميل البيانات بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في إعادة تحميل البيانات: $e');
      // في حالة الفشل، استخدم البيانات المحلية
      if (_categories.isEmpty) {
        AppLogger.warning('جاري استخدام البيانات المحلية كحل بديل...');
        await useLocalData();
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // دالة لاستخدام البيانات المحلية
  Future<void> useLocalData() async {
    AppLogger.info('بدء استخدام البيانات المحلية...');

    try {
      // إعادة تهيئة قاعدة البيانات
      AppLogger.info('جاري إعادة تهيئة قاعدة البيانات...');
      await _databaseService.checkAndReinitializeDatabase();
      AppLogger.info('تم إعادة تهيئة قاعدة البيانات بنجاح');

      // محاولة تحميل التصنيفات مرة أخرى
      AppLogger.info('جاري تحميل التصنيفات...');
      _categories = await _databaseService.getCategories();
      AppLogger.info('تم تحميل ${_categories.length} تصنيف');

      // إذا كانت التصنيفات فارغة، استخدم البيانات المحلية مباشرة
      if (_categories.isEmpty) {
        AppLogger.warning(
          'لا توجد تصنيفات، جاري استخدام البيانات المحلية مباشرة...',
        );

        // استخدام بيانات التصنيفات من ملف categories_data.dart
        AppLogger.info(
          'جاري استخدام بيانات التصنيفات من ملف categories_data.dart...',
        );
        _categories = List.from(categoriesData);
        AppLogger.info(
          'تم تحميل ${_categories.length} تصنيف من البيانات المحلية',
        );
      }

      // إذا كان هناك تصنيف محدد حالياً، قم بتحميل الأذكار المرتبطة به
      if (_currentCategory.isNotEmpty) {
        AppLogger.info(
          'جاري تحميل الأذكار المرتبطة بالتصنيف: $_currentCategory',
        );
        _currentAzkar =
            azkarData
                .where((zikr) => zikr.category == _currentCategory)
                .toList();
        AppLogger.info(
          'تم تحميل ${_currentAzkar.length} ذكر للتصنيف: $_currentCategory',
        );
      }

      // تحميل ذكر اليوم من البيانات المحلية
      if (_dailyZikr == null && azkarData.isNotEmpty) {
        AppLogger.info('جاري تحميل ذكر اليوم من البيانات المحلية...');
        final random = DateTime.now().day % azkarData.length;
        _dailyZikr = azkarData[random];
        AppLogger.info(
          'تم تحميل ذكر اليوم: ${_dailyZikr?.text.substring(0, 20)}...',
        );
      }

      AppLogger.info('تم استخدام البيانات المحلية بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في استخدام البيانات المحلية: $e');

      // في حالة الفشل، استخدم البيانات المحلية مباشرة
      _categories = List.from(categoriesData);

      if (_currentCategory.isNotEmpty) {
        _currentAzkar =
            azkarData
                .where((zikr) => zikr.category == _currentCategory)
                .toList();
      }

      if (_dailyZikr == null && azkarData.isNotEmpty) {
        final random = DateTime.now().day % azkarData.length;
        _dailyZikr = azkarData[random];
      }
    }

    notifyListeners();
  }

  Future<void> loadAzkarByCategory(String category) async {
    _isLoading = true;
    _currentCategory = category;
    notifyListeners();

    try {
      AppLogger.info('بدء تحميل الأذكار للتصنيف: $category');

      // محاولة تحميل الأذكار من قاعدة البيانات
      _currentAzkar = await _databaseService.getAzkarByCategory(category);
      AppLogger.info(
        'تم تحميل ${_currentAzkar.length} ذكر من قاعدة البيانات للتصنيف: $category',
      );

      // إذا لم يتم العثور على أذكار، استخدم البيانات المحلية
      if (_currentAzkar.isEmpty) {
        AppLogger.info(
          'لا توجد أذكار في قاعدة البيانات للتصنيف: $category، جاري استخدام البيانات المحلية',
        );
        _currentAzkar =
            azkarData.where((zikr) => zikr.category == category).toList();
        AppLogger.info(
          'تم تحميل ${_currentAzkar.length} ذكر من البيانات المحلية للتصنيف: $category',
        );

        // إذا كان التصنيف جديداً ولا توجد أذكار محلية له، قم بإنشاء قائمة فارغة
        if (_currentAzkar.isEmpty) {
          AppLogger.info(
            'لا توجد أذكار محلية للتصنيف: $category، جاري إنشاء قائمة فارغة',
          );
          _currentAzkar = [];
        }
      }

      // طباعة معلومات الأذكار المحملة
      for (var zikr in _currentAzkar) {
        AppLogger.info(
          'الذكر: ${zikr.id}, النص: ${zikr.text.substring(0, min(20, zikr.text.length))}...',
        );
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل الأذكار للتصنيف: $category: $e');

      // في حالة حدوث خطأ، استخدم البيانات المحلية
      _currentAzkar =
          azkarData.where((zikr) => zikr.category == category).toList();
      AppLogger.info(
        'تم تحميل ${_currentAzkar.length} ذكر من البيانات المحلية للتصنيف: $category (بعد الخطأ)',
      );

      // إذا كان التصنيف جديداً ولا توجد أذكار محلية له، قم بإنشاء قائمة فارغة
      if (_currentAzkar.isEmpty) {
        AppLogger.info(
          'لا توجد أذكار محلية للتصنيف: $category، جاري إنشاء قائمة فارغة',
        );
        _currentAzkar = [];
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _loadFavoriteAzkar() async {
    // الحصول على جميع الأذكار المفضلة
    List<Zikr> allFavorites = await _databaseService.getFavoriteAzkar();

    // استبعاد الأذكار الخاصة من قائمة المفضلة
    _favoriteAzkar =
        allFavorites.where((zikr) => zikr.category != 'أذكاري الخاصة').toList();

    notifyListeners();
  }

  Future<void> toggleFavorite(Zikr zikr) async {
    // إنشاء نسخة محدثة من الذكر مع تبديل حالة المفضلة
    final updatedZikr = zikr.copyWith(isFavorite: !zikr.isFavorite);

    // تحديث في القائمة الحالية فوراً للاستجابة السريعة
    final index = _currentAzkar.indexWhere((item) => item.id == zikr.id);
    if (index != -1) {
      _currentAzkar[index] = updatedZikr;
    }

    // تحديث في قائمة المفضلة فوراً
    if (updatedZikr.isFavorite) {
      // إضافة إلى المفضلة
      if (!_favoriteAzkar.any((item) => item.id == updatedZikr.id)) {
        _favoriteAzkar.add(updatedZikr);
      }
    } else {
      // إزالة من المفضلة
      _favoriteAzkar.removeWhere((item) => item.id == zikr.id);
    }

    // إخطار المستمعين فوراً لتحديث واجهة المستخدم
    notifyListeners();

    try {
      // تحديث في قاعدة البيانات (بشكل غير متزامن)
      await _databaseService.toggleFavorite(zikr.id, !zikr.isFavorite);

      // تم تنفيذ العملية بنجاح
    } catch (e) {
      // في حالة حدوث خطأ، نعيد الحالة إلى ما كانت عليه

      // إعادة الحالة في القائمة الحالية
      if (index != -1) {
        _currentAzkar[index] = zikr;
      }

      // إعادة الحالة في قائمة المفضلة
      if (!updatedZikr.isFavorite) {
        // كان في المفضلة وتمت إزالته، نعيده
        if (!_favoriteAzkar.any((item) => item.id == zikr.id)) {
          _favoriteAzkar.add(zikr);
        }
      } else {
        // لم يكن في المفضلة وتمت إضافته، نزيله
        _favoriteAzkar.removeWhere((item) => item.id == zikr.id);
      }

      // إخطار المستمعين مرة أخرى بعد إعادة الحالة
      notifyListeners();
    }
  }

  Future<void> updateZikrCount(
    Zikr zikr,
    int count, [
    BuildContext? context,
  ]) async {
    final updatedZikr = zikr.copyWith(currentCount: count);

    // Update in database
    await _databaseService.updateCurrentCount(zikr.id, count);

    // Update in current list
    final index = _currentAzkar.indexWhere((item) => item.id == zikr.id);
    if (index != -1) {
      _currentAzkar[index] = updatedZikr;

      // Update completion status for morning and evening azkar
      if (_currentCategory == 'أذكار الصباح' ||
          _currentCategory == 'أذكار المساء') {
        final completedCount =
            _currentAzkar.where((z) => z.currentCount >= z.count).length;
        await _updateAzkarCompletionStatus(_currentCategory, completedCount);
      }
    }

    // Update in favorites list if exists
    final favIndex = _favoriteAzkar.indexWhere((item) => item.id == zikr.id);
    if (favIndex != -1) {
      _favoriteAzkar[favIndex] = updatedZikr;
    }

    // تسجيل إكمال الذكر في الإحصائيات
    if (_statisticsProvider != null && count >= zikr.count) {
      await _statisticsProvider!.recordZikrCompletion(
        completedCount: 1,
        totalCount: 1,
        category: _currentCategory,
        timeSpent: const Duration(minutes: 1), // تقدير الوقت
      );
    }

    // تسجيل إكمال الذكر في الإحصائيات الشاملة
    if (count >= zikr.count) {
      final totalAzkarInCategory = _currentAzkar.length;
      final completedAzkarInCategory =
          _currentAzkar.where((z) => z.currentCount >= z.count).length;
      await _comprehensiveStatsService.recordZikrCompletion(
        _currentCategory,
        completedAzkarInCategory,
        totalAzkarInCategory,
      );
    }

    // التحقق من إكمال جميع الأذكار في التصنيف
    if (context != null && context.mounted) {
      await _checkCategoryCompletion(context);
    }

    notifyListeners();
  }

  Future<void> resetAllCounts() async {
    await _databaseService.resetAllCurrentCounts();

    // Reset counts in current list
    for (int i = 0; i < _currentAzkar.length; i++) {
      _currentAzkar[i] = _currentAzkar[i].copyWith(currentCount: 0);
    }

    // Reset counts in favorites list
    for (int i = 0; i < _favoriteAzkar.length; i++) {
      _favoriteAzkar[i] = _favoriteAzkar[i].copyWith(currentCount: 0);
    }

    notifyListeners();
  }

  Future<List<Zikr>> searchAzkar(String query) async {
    if (query.isEmpty) return [];
    return await _databaseService.searchAzkar(query);
  }

  // ===== دوال الأذكار الخاصة =====

  // تحميل الأذكار الخاصة
  Future<void> _loadCustomAzkar() async {
    try {
      // استخدام خدمة التخزين المحلي للحصول على الأذكار الخاصة
      _customAzkar = await _localStorageService.getCustomAzkar();
      notifyListeners();
    } catch (e) {
      // خطأ في تحميل الأذكار الخاصة
      _customAzkar = []; // تعيين قائمة فارغة في حالة الخطأ
    }
  }

  // إضافة ذكر خاص جديد
  Future<bool> addCustomZikr(Zikr zikr) async {
    try {
      AppLogger.info('AzkarProvider: بدء إضافة ذكر خاص جديد: ${zikr.text}');

      // التأكد من تحميل الأذكار الخاصة أولاً
      if (_customAzkar.isEmpty) {
        await _loadCustomAzkar();
      }

      // إنشاء معرف جديد للذكر
      int newId = DateTime.now().millisecondsSinceEpoch;

      // إنشاء نسخة جديدة من الذكر مع المعرف الجديد وتصنيف "أذكاري الخاصة"
      final newZikr = zikr.copyWith(
        id: newId,
        category: 'أذكاري الخاصة',
        source: 'أذكاري الخاصة',
        isCustom: true, // تعيين علامة أنه ذكر مخصص
      );

      AppLogger.info('AzkarProvider: تم إنشاء ذكر جديد بمعرف: $newId');

      // استخدام خدمة التخزين المحلي لإضافة الذكر الخاص
      final id = await _localStorageService.addCustomZikr(newZikr);
      AppLogger.info('AzkarProvider: نتيجة إضافة الذكر الخاص: $id');

      if (id > 0) {
        // إضافة الذكر الجديد إلى القائمة المحلية
        _customAzkar.insert(0, newZikr); // إضافة في بداية القائمة
        notifyListeners();
        AppLogger.info('AzkarProvider: تم إضافة الذكر الخاص بنجاح');
        return true;
      }

      AppLogger.error(
        'AzkarProvider: فشل في إضافة الذكر الخاص، المعرف غير صالح: $id',
      );
      return false;
    } catch (e) {
      AppLogger.error('AzkarProvider: خطأ في إضافة الذكر الخاص: $e');
      return false;
    }
  }

  // تحديث ذكر خاص
  Future<bool> updateCustomZikr(Zikr zikr) async {
    try {
      // استخدام خدمة التخزين المحلي لتحديث الذكر الخاص
      final result = await _localStorageService.updateCustomZikr(zikr);

      if (result) {
        // تحديث الذكر في القائمة المحلية
        final index = _customAzkar.indexWhere((item) => item.id == zikr.id);
        if (index != -1) {
          _customAzkar[index] = zikr;
          notifyListeners();
        }
        return true;
      }
      return false;
    } catch (e) {
      // خطأ في تحديث الذكر الخاص
      return false;
    }
  }

  // حذف ذكر خاص
  Future<bool> deleteCustomZikr(int id) async {
    try {
      // استخدام خدمة التخزين المحلي لحذف الذكر الخاص
      final result = await _localStorageService.deleteCustomZikr(id);

      if (result) {
        // حذف الذكر من القائمة المحلية
        _customAzkar.removeWhere((item) => item.id == id);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      // خطأ في حذف الذكر الخاص
      return false;
    }
  }

  // تحديث عداد الذكر الخاص
  Future<void> updateCustomZikrCount(Zikr zikr, int count) async {
    try {
      // استخدام خدمة التخزين المحلي لتحديث عداد الذكر
      await _localStorageService.updateZikrCount(zikr, count);

      // تحديث في القائمة المحلية
      final updatedZikr = zikr.copyWith(currentCount: count);
      final index = _customAzkar.indexWhere((item) => item.id == zikr.id);
      if (index != -1) {
        _customAzkar[index] = updatedZikr;
        notifyListeners();
      }
    } catch (e) {
      // خطأ في تحديث عداد الذكر الخاص
    }
  }

  // تبديل حالة المفضلة للذكر الخاص
  Future<void> toggleCustomZikrFavorite(Zikr zikr) async {
    try {
      // إنشاء نسخة محدثة من الذكر مع تبديل حالة المفضلة
      final updatedZikr = zikr.copyWith(isFavorite: !zikr.isFavorite);

      // تحديث في القائمة المحلية فوراً للاستجابة السريعة
      final index = _customAzkar.indexWhere((item) => item.id == zikr.id);
      if (index != -1) {
        _customAzkar[index] = updatedZikr;
        notifyListeners();
      }

      // استخدام خدمة التخزين المحلي لتبديل حالة المفضلة
      await _localStorageService.toggleFavorite(zikr);
    } catch (e) {
      // خطأ في تبديل حالة المفضلة للذكر الخاص
    }
  }

  Future<void> _loadDailyZikr() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      // الحصول على التاريخ الحالي
      DateTime now = DateTime.now();
      String today = now.toString().split(' ')[0]; // Get current date
      String lastDailyZikrDate = prefs.getString('last_daily_zikr_date') ?? '';

      // التحقق من وقت آخر تحديث
      String lastUpdateTimeStr =
          prefs.getString('last_daily_zikr_update_time') ?? '';
      DateTime lastUpdateTime =
          lastUpdateTimeStr.isNotEmpty
              ? DateTime.parse(lastUpdateTimeStr)
              : DateTime(2000); // تاريخ قديم للتأكد من التحديث

      // التحقق مما إذا كان يجب تحديث ذكر اليوم (يوم جديد أو مرت 24 ساعة)
      bool shouldUpdate =
          today != lastDailyZikrDate ||
          now.difference(lastUpdateTime).inHours >= 24;

      // التحقق مما إذا كان الوقت بعد منتصف الليل وقبل التحديث
      bool isAfterMidnight = now.hour < 12 && lastUpdateTime.day != now.day;

      if (shouldUpdate || isAfterMidnight) {
        AppLogger.info(
          'تحديث ذكر اليوم: يوم جديد أو مرت 24 ساعة أو بعد منتصف الليل',
        );

        // احصل على ذكر عشوائي جديد
        _dailyZikr = await _databaseService.getRandomZikr();

        // حفظ التاريخ ووقت التحديث
        await prefs.setString('last_daily_zikr_date', today);
        await prefs.setString(
          'last_daily_zikr_update_time',
          now.toIso8601String(),
        );

        // حفظ معرف ذكر اليوم لاسترجاعه لاحقًا
        await prefs.setInt('daily_zikr_id', _dailyZikr!.id);

        // إعادة ضبط حالة إكمال أذكار الصباح والمساء إذا كان يوم جديد
        await _resetDailyAzkarCompletionStatus();

        AppLogger.info(
          'تم تحديث ذكر اليوم: ${_dailyZikr!.text.substring(0, min(30, _dailyZikr!.text.length))}...',
        );
      } else {
        AppLogger.info('استخدام ذكر اليوم المحفوظ من قبل');

        // إذا كان نفس اليوم، استرجع ذكر اليوم المحفوظ
        int dailyZikrId = prefs.getInt('daily_zikr_id') ?? 1;

        try {
          // محاولة الحصول على الذكر بواسطة المعرف
          if (_categories.isNotEmpty) {
            // استخدام جميع التصنيفات للبحث عن الذكر
            for (var category in _categories) {
              List<Zikr> categoryAzkar = await _databaseService
                  .getAzkarByCategory(category.name);

              // البحث عن الذكر في هذا التصنيف
              Zikr? foundZikr = categoryAzkar.firstWhere(
                (zikr) => zikr.id == dailyZikrId,
                orElse: () => Zikr.empty(),
              );

              // إذا وجدنا الذكر، استخدمه
              if (foundZikr.id == dailyZikrId) {
                _dailyZikr = foundZikr;
                break;
              }
            }

            // إذا لم نجد الذكر في أي تصنيف، احصل على ذكر عشوائي
            if (_dailyZikr == null || _dailyZikr!.id != dailyZikrId) {
              _dailyZikr = await _databaseService.getRandomZikr();
            }
          } else {
            // إذا لم تكن هناك تصنيفات، احصل على ذكر عشوائي
            _dailyZikr = await _databaseService.getRandomZikr();
          }
        } catch (e) {
          AppLogger.error('خطأ في استرجاع ذكر اليوم: $e');
          // في حالة حدوث خطأ، احصل على ذكر عشوائي
          _dailyZikr = await _databaseService.getRandomZikr();
        }
      }

      // تحميل حالة إكمال الأذكار
      await _loadAzkarCompletionStatus();

      notifyListeners();
    } catch (e) {
      AppLogger.error('خطأ في تحميل ذكر اليوم: $e');
      // في حالة حدوث خطأ، نحاول الحصول على ذكر عشوائي
      try {
        _dailyZikr = await _databaseService.getRandomZikr();
        notifyListeners();
      } catch (e2) {
        AppLogger.error('فشل في الحصول على ذكر عشوائي: $e2');
      }
    }
  }

  // تحميل حالة إكمال الأذكار
  Future<void> _loadAzkarCompletionStatus() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      // تحميل حالة إكمال أذكار الصباح
      final morningCompletionJson = prefs.getString('morning_azkar_completion');
      if (morningCompletionJson != null) {
        final morningCompletionMap = Map<String, dynamic>.from(
          Map.castFrom(json.decode(morningCompletionJson)),
        );
        _azkarCompletionStatus['أذكار الصباح'] = AzkarCompletionModel.fromMap(
          morningCompletionMap,
        );
      } else {
        // إنشاء حالة إكمال جديدة لأذكار الصباح
        _azkarCompletionStatus['أذكار الصباح'] = AzkarCompletionModel(
          category: 'أذكار الصباح',
          totalCount:
              _currentCategory == 'أذكار الصباح' ? _currentAzkar.length : 31,
          completedCount: 0,
        );
      }

      // تحميل حالة إكمال أذكار المساء
      final eveningCompletionJson = prefs.getString('evening_azkar_completion');
      if (eveningCompletionJson != null) {
        final eveningCompletionMap = Map<String, dynamic>.from(
          Map.castFrom(json.decode(eveningCompletionJson)),
        );
        _azkarCompletionStatus['أذكار المساء'] = AzkarCompletionModel.fromMap(
          eveningCompletionMap,
        );
      } else {
        // إنشاء حالة إكمال جديدة لأذكار المساء
        _azkarCompletionStatus['أذكار المساء'] = AzkarCompletionModel(
          category: 'أذكار المساء',
          totalCount:
              _currentCategory == 'أذكار المساء' ? _currentAzkar.length : 31,
          completedCount: 0,
        );
      }
    } catch (e) {
      // في حالة حدوث خطأ، إنشاء حالات إكمال افتراضية
      _azkarCompletionStatus['أذكار الصباح'] = AzkarCompletionModel(
        category: 'أذكار الصباح',
        totalCount: 31,
        completedCount: 0,
      );

      _azkarCompletionStatus['أذكار المساء'] = AzkarCompletionModel(
        category: 'أذكار المساء',
        totalCount: 31,
        completedCount: 0,
      );
    }
  }

  // إعادة ضبط حالة إكمال أذكار الصباح والمساء
  Future<void> _resetDailyAzkarCompletionStatus() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      // التحقق مما إذا كان يجب إعادة ضبط أذكار الصباح
      final shouldResetMorning = await _shouldResetMorningAzkar();
      if (shouldResetMorning) {
        AppLogger.info('إعادة ضبط أذكار الصباح تلقائياً');
        _azkarCompletionStatus['أذكار الصباح'] = AzkarCompletionModel(
          category: 'أذكار الصباح',
          totalCount:
              _currentCategory == 'أذكار الصباح' ? _currentAzkar.length : 31,
          completedCount: 0,
        );

        // حفظ حالة إكمال أذكار الصباح
        await prefs.setString(
          'morning_azkar_completion',
          json.encode(_azkarCompletionStatus['أذكار الصباح']!.toMap()),
        );

        // إعادة ضبط عدادات أذكار الصباح
        if (_currentCategory == 'أذكار الصباح') {
          await _resetCategoryCounters('أذكار الصباح');
        }

        // حفظ تاريخ آخر إعادة ضبط
        await prefs.setString(
          'morning_azkar_reset',
          DateTime.now().toIso8601String(),
        );
      }

      // التحقق مما إذا كان يجب إعادة ضبط أذكار المساء
      final shouldResetEvening = await _shouldResetEveningAzkar();
      if (shouldResetEvening) {
        AppLogger.info('إعادة ضبط أذكار المساء تلقائياً');
        _azkarCompletionStatus['أذكار المساء'] = AzkarCompletionModel(
          category: 'أذكار المساء',
          totalCount:
              _currentCategory == 'أذكار المساء' ? _currentAzkar.length : 31,
          completedCount: 0,
        );

        // حفظ حالة إكمال أذكار المساء
        await prefs.setString(
          'evening_azkar_completion',
          json.encode(_azkarCompletionStatus['أذكار المساء']!.toMap()),
        );

        // إعادة ضبط عدادات أذكار المساء
        if (_currentCategory == 'أذكار المساء') {
          await _resetCategoryCounters('أذكار المساء');
        }

        // حفظ تاريخ آخر إعادة ضبط
        await prefs.setString(
          'evening_azkar_reset',
          DateTime.now().toIso8601String(),
        );
      }
    } catch (e) {
      AppLogger.error('خطأ في إعادة ضبط حالة إكمال الأذكار: $e');
    }
  }

  // التحقق مما إذا كان يجب إعادة ضبط أذكار الصباح
  Future<bool> _shouldResetMorningAzkar() async {
    // إذا كان الوقت الحالي هو وقت الصباح، تحقق مما إذا كان يوم جديد
    if (AzkarCompletionModel.isMorningTime()) {
      final now = DateTime.now();
      final lastReset = await _getLastResetDate('morning_azkar_reset');

      // إعادة ضبط إذا كان يوم جديد أو لم يتم إعادة الضبط من قبل
      if (lastReset == null ||
          lastReset.year != now.year ||
          lastReset.month != now.month ||
          lastReset.day != now.day) {
        return true;
      }
    }

    return false;
  }

  // التحقق مما إذا كان يجب إعادة ضبط أذكار المساء
  Future<bool> _shouldResetEveningAzkar() async {
    // إذا كان الوقت الحالي هو وقت المساء، تحقق مما إذا كان يوم جديد أو فترة جديدة
    if (AzkarCompletionModel.isEveningTime()) {
      final now = DateTime.now();
      final lastReset = await _getLastResetDate('evening_azkar_reset');

      // إعادة ضبط إذا كان يوم جديد أو لم يتم إعادة الضبط من قبل
      if (lastReset == null ||
          lastReset.year != now.year ||
          lastReset.month != now.month ||
          lastReset.day != now.day) {
        return true;
      }
    }

    return false;
  }

  // الحصول على تاريخ آخر إعادة ضبط
  Future<DateTime?> _getLastResetDate(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dateString = prefs.getString(key);
      if (dateString != null) {
        return DateTime.parse(dateString);
      }
      return null;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على تاريخ آخر إعادة ضبط: $e');
      return null;
    }
  }

  // إعادة ضبط عدادات صنف معين
  Future<void> _resetCategoryCounters(String category) async {
    try {
      final db = await _databaseService.database;

      // تحديث عدادات الأذكار في قاعدة البيانات
      await db.rawUpdate(
        'UPDATE azkar SET currentCount = 0 WHERE category = ?',
        [category],
      );

      // تحديث عدادات الأذكار في الذاكرة
      if (_currentCategory == category) {
        for (var i = 0; i < _currentAzkar.length; i++) {
          _currentAzkar[i] = _currentAzkar[i].copyWith(currentCount: 0);
        }
      }

      AppLogger.info('تم إعادة ضبط عدادات صنف $category');
    } catch (e) {
      AppLogger.error('خطأ في إعادة ضبط عدادات الصنف: $e');
    }
  }

  // تحديث حالة إكمال الأذكار عند زيادة عداد ذكر
  Future<void> _updateAzkarCompletionStatus(
    String category,
    int completedCount,
  ) async {
    try {
      // التحقق من أن الصنف هو أذكار الصباح أو المساء
      if (category != 'أذكار الصباح' && category != 'أذكار المساء') {
        return;
      }

      SharedPreferences prefs = await SharedPreferences.getInstance();

      // تحديث حالة إكمال الأذكار
      final totalCount = _currentAzkar.length;
      final currentCompletionStatus = _azkarCompletionStatus[category];

      if (currentCompletionStatus != null) {
        // تحديث عدد الأذكار المكتملة
        final updatedCompletionStatus = currentCompletionStatus.copyWith(
          completedCount: completedCount,
          totalCount: totalCount,
          lastCompletionDate: DateTime.now(),
        );

        // حفظ حالة إكمال الأذكار
        _azkarCompletionStatus[category] = updatedCompletionStatus;

        // حفظ حالة إكمال الأذكار في التخزين المحلي
        await prefs.setString(
          category == 'أذكار الصباح'
              ? 'morning_azkar_completion'
              : 'evening_azkar_completion',
          json.encode(updatedCompletionStatus.toMap()),
        );

        // التحقق مما إذا كانت جميع الأذكار مكتملة
        if (updatedCompletionStatus.isFullyCompleted) {
          // إطلاق حدث اكتمال الأذكار
          _completionController.add(updatedCompletionStatus);

          // تفعيل حالة حركة الاكتمال
          _isCompletionAnimationActive = true;
          notifyListeners();

          // إيقاف حالة حركة الاكتمال بعد 5 ثوانٍ
          Future.delayed(const Duration(seconds: 5), () {
            _isCompletionAnimationActive = false;
            notifyListeners();
          });
        }
      }
    } catch (e) {
      // في حالة حدوث خطأ، تجاهل تحديث حالة الإكمال
    }
  }

  // إضافة تصنيف جديد
  Future<bool> addNewCategory(String name, String icon) async {
    try {
      AppLogger.info('بدء إضافة تصنيف جديد: الاسم=$name، الأيقونة=$icon');

      // التحقق من صحة البيانات
      if (name.trim().isEmpty) {
        AppLogger.warning('اسم التصنيف فارغ');
        return false;
      }

      // التحقق من عدم وجود تصنيف بنفس الاسم في الذاكرة
      if (_categories.any((category) => category.name.trim() == name.trim())) {
        AppLogger.warning('يوجد تصنيف بنفس الاسم في الذاكرة: $name');
        return false;
      }

      // التحقق من صحة الأيقونة
      String finalIcon = icon;
      if (icon.trim().isEmpty) {
        finalIcon = 'assets/icons/category.png'; // استخدام أيقونة افتراضية
        AppLogger.info('تم استخدام أيقونة افتراضية: $finalIcon');
      } else if (!icon.startsWith('assets/')) {
        // تصحيح مسار الأيقونة
        if (!icon.endsWith('.png')) {
          finalIcon = 'assets/icons/$icon.png';
        } else {
          finalIcon = 'assets/icons/$icon';
        }
        AppLogger.info('تم تصحيح مسار الأيقونة: $finalIcon');
      }

      // التأكد من وجود الملف
      try {
        // طباعة مسار الأيقونة للتشخيص
        AppLogger.info('مسار الأيقونة النهائي: $finalIcon');
      } catch (e) {
        AppLogger.warning('خطأ في التحقق من وجود الأيقونة: $e');
        // استخدام أيقونة افتراضية في حالة الخطأ
        finalIcon = 'assets/icons/category.png';
      }

      // إنشاء كائن التصنيف الجديد
      final newCategory = Category(
        id: 0, // سيتم تحديده في قاعدة البيانات
        name: name.trim(),
        icon: finalIcon,
        count: 0,
        description: name.trim(),
      );

      AppLogger.info('تم إنشاء كائن التصنيف الجديد: ${newCategory.toMap()}');

      // إضافة التصنيف إلى قاعدة البيانات
      int id = await _databaseService.addCategory(newCategory);
      AppLogger.info('نتيجة إضافة التصنيف إلى قاعدة البيانات: $id');

      // التحقق من نتيجة الإضافة
      if (id == -2) {
        AppLogger.warning('يوجد تصنيف بنفس الاسم في قاعدة البيانات: $name');
        return false;
      } else if (id == -3) {
        AppLogger.warning('اسم التصنيف فارغ');
        return false;
      } else if (id <= 0) {
        AppLogger.warning('فشل في إضافة التصنيف إلى قاعدة البيانات: $id');

        // محاولة إضافة التصنيف مرة أخرى بأيقونة افتراضية
        if (finalIcon != 'assets/icons/category.png') {
          AppLogger.info('محاولة إضافة التصنيف مرة أخرى بأيقونة افتراضية');
          final newCategoryWithDefaultIcon = newCategory.copyWith(
            icon: 'assets/icons/category.png',
          );
          final retryId = await _databaseService.addCategory(
            newCategoryWithDefaultIcon,
          );

          if (retryId > 0) {
            AppLogger.info(
              'تم إضافة التصنيف بنجاح باستخدام الأيقونة الافتراضية: $retryId',
            );
            // تحديث المعرف للاستخدام لاحقًا
            id = retryId;
          } else {
            AppLogger.warning(
              'فشل في إضافة التصنيف حتى مع الأيقونة الافتراضية',
            );
            return false;
          }
        } else {
          return false;
        }
      }

      // إضافة التصنيف إلى القائمة المحلية
      final addedCategory = newCategory.copyWith(id: id);
      _categories.add(addedCategory);

      // طباعة قائمة التصنيفات بعد الإضافة
      AppLogger.info(
        'تم إضافة التصنيف إلى القائمة المحلية. عدد التصنيفات: ${_categories.length}',
      );
      for (var category in _categories) {
        AppLogger.info(
          'التصنيف: ${category.name}, الأيقونة: ${category.icon}, المعرف: ${category.id}',
        );
      }

      // إعادة ترتيب التصنيفات
      _categories.sort((a, b) => a.id.compareTo(b.id));

      // إعادة تحميل البيانات للتأكد من تحديث جميع القوائم
      await reloadData();

      return true;
    } catch (e) {
      AppLogger.error('خطأ في إضافة تصنيف جديد: $e');
      return false;
    }
  }

  // إضافة ذكر إلى تصنيف
  Future<bool> addZikrToCategory(
    String categoryName,
    String text,
    int count,
    String source,
    String fadl,
  ) async {
    try {
      // التحقق من وجود التصنيف
      if (!_categories.any((category) => category.name == categoryName)) {
        return false;
      }

      // إنشاء كائن الذكر الجديد
      final newZikr = Zikr(
        id: 0, // سيتم تحديده في قاعدة البيانات
        text: text,
        count: count,
        source: source,
        category: categoryName,
        reference: '',
        fadl: fadl,
        isFavorite: false,
        currentCount: 0,
      );

      // إضافة الذكر إلى قاعدة البيانات
      final id = await _databaseService.addZikrToCategory(
        newZikr,
        categoryName,
      );

      if (id > 0) {
        // إذا كان التصنيف الحالي هو نفس التصنيف المضاف إليه، قم بتحديث القائمة المحلية
        if (_currentCategory == categoryName) {
          final addedZikr = newZikr.copyWith(id: id);
          _currentAzkar.add(addedZikr);
          notifyListeners();
        }

        // تحديث عدد الأذكار في التصنيف
        final categoryIndex = _categories.indexWhere(
          (category) => category.name == categoryName,
        );
        if (categoryIndex != -1) {
          final updatedCategory = _categories[categoryIndex].copyWith(
            count: _categories[categoryIndex].count + 1,
          );
          _categories[categoryIndex] = updatedCategory;
          notifyListeners();
        }

        return true;
      }

      return false;
    } catch (e) {
      AppLogger.error('خطأ في إضافة ذكر إلى تصنيف: $e');
      return false;
    }
  }

  /// التحقق من إكمال جميع الأذكار في التصنيف وعرض النافذة المنبثقة
  Future<void> _checkCategoryCompletion(BuildContext context) async {
    if (_currentAzkar.isEmpty || _currentCategory.isEmpty) return;

    // حساب عدد الأذكار المكتملة
    final completedAzkar =
        _currentAzkar.where((zikr) => zikr.currentCount >= zikr.count).toList();
    final totalAzkar = _currentAzkar.length;

    // التحقق من إكمال جميع الأذكار
    if (completedAzkar.length == totalAzkar && totalAzkar > 0) {
      // حساب النقاط المكتسبة
      final earnedPoints = completedAzkar.fold<int>(
        0,
        (sum, zikr) => sum + (zikr.count * 10),
      );

      // تسجيل إكمال التصنيف في الإحصائيات
      if (_statisticsProvider != null) {
        await _statisticsProvider!.recordCategoryCompletion(_currentCategory);
      }

      // عرض النافذة المنبثقة
      if (context.mounted) {
        await _showCelebrationDialog(
          context,
          _currentCategory,
          totalAzkar,
          earnedPoints,
        );
      }

      AppLogger.info('تم إكمال جميع أذكار تصنيف: $_currentCategory');
    }
  }

  /// عرض نافذة الاحتفال
  Future<void> _showCelebrationDialog(
    BuildContext context,
    String categoryName,
    int completedCount,
    int earnedPoints,
  ) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withAlpha(128),
      builder:
          (BuildContext context) => CompletionCelebrationDialog(
            categoryName: categoryName,
            completedCount: completedCount,
            earnedPoints: earnedPoints,
            onClosed: () {
              AppLogger.info('تم إغلاق نافذة الاحتفال لتصنيف: $categoryName');
            },
          ),
    );
  }
}
