import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';

/// خدمة إعدادات الأذكار
class AzkarSettingsService {
  static const String _hideCompletedKey = 'hide_completed_azkar';
  static const String _vibrationEnabledKey = 'vibration_enabled';

  /// الحصول على إعداد إخفاء الأذكار المكتملة
  static Future<bool> getHideCompletedAzkar() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_hideCompletedKey) ?? false;
  }

  /// حفظ إعداد إخفاء الأذكار المكتملة
  static Future<void> setHideCompletedAzkar(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hideCompletedKey, value);
  }

  /// الحصول على إعداد الاهتزاز
  static Future<bool> getVibrationEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_vibrationEnabledKey) ?? true; // افتراضياً مفعل
  }

  /// حفظ إعداد الاهتزاز
  static Future<void> setVibrationEnabled(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_vibrationEnabledKey, value);
  }

  /// تنفيذ الاهتزاز إذا كان مفعلاً
  static Future<void> performVibration() async {
    final isEnabled = await getVibrationEnabled();
    if (isEnabled) {
      try {
        // استخدام HapticFeedback للاهتزاز اللطيف
        await HapticFeedback.lightImpact();
      } catch (e) {
        // في حالة فشل الاهتزاز، نتجاهل الخطأ
      }
    }
  }

  /// تنفيذ اهتزاز متوسط للأحداث المهمة
  static Future<void> performMediumVibration() async {
    final isEnabled = await getVibrationEnabled();
    if (isEnabled) {
      try {
        await HapticFeedback.mediumImpact();
      } catch (e) {
        // في حالة فشل الاهتزاز، نتجاهل الخطأ
      }
    }
  }

  /// تنفيذ اهتزاز قوي للأحداث الخاصة
  static Future<void> performHeavyVibration() async {
    final isEnabled = await getVibrationEnabled();
    if (isEnabled) {
      try {
        await HapticFeedback.heavyImpact();
      } catch (e) {
        // في حالة فشل الاهتزاز، نتجاهل الخطأ
      }
    }
  }

  /// الحصول على جميع الإعدادات دفعة واحدة
  static Future<Map<String, bool>> getAllSettings() async {
    final hideCompleted = await getHideCompletedAzkar();
    final vibrationEnabled = await getVibrationEnabled();
    
    return {
      'hideCompleted': hideCompleted,
      'vibrationEnabled': vibrationEnabled,
    };
  }

  /// حفظ جميع الإعدادات دفعة واحدة
  static Future<void> saveAllSettings({
    required bool hideCompleted,
    required bool vibrationEnabled,
  }) async {
    await Future.wait([
      setHideCompletedAzkar(hideCompleted),
      setVibrationEnabled(vibrationEnabled),
    ]);
  }
}
