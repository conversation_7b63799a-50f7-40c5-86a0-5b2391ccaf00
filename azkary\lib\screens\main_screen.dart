import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'home_screen.dart';
import 'quran_screen_new.dart';
import 'more_screen.dart';
import 'custom_dua_screen.dart';
import '../widgets/custom_icon.dart';
import '../widgets/app_drawer.dart';
import '../widgets/daily_ayah_dialog.dart';
import '../widgets/islamic_background.dart';
import '../services/daily_ayah_provider.dart';

class MainScreen extends StatefulWidget {
  final bool showDailyAyah;

  const MainScreen({super.key, this.showDailyAyah = false});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // متحكم الحركة للانتقال بين الصفحات
  final PageController _pageController = PageController();

  // متغير لتتبع ما إذا كان الانتقال بين الصفحات قيد التنفيذ
  bool _isPageChanging = false;

  final List<Widget> _screens = [
    const HomeScreen(),
    const QuranScreen(),
    const CustomDuaScreen(),
    const MoreScreen(),
  ];

  @override
  void initState() {
    super.initState();

    // Mostrar الآية اليومية (Aleya diaria) si se solicita
    if (widget.showDailyAyah) {
      // Esperar a que se construya la interfaz antes de mostrar el diálogo
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showDailyAyah();
      });
    }
  }

  // عرض نافذة الآية اليومية (Mostrar diálogo de Aleya diaria)
  void _showDailyAyah() {
    // تهيئة مزود الآية اليومية (Inicializar proveedor de Aleya diaria)
    final provider = Provider.of<DailyAyahProvider>(context, listen: false);
    provider.initialize();

    // عرض نافذة الآية اليومية (Mostrar diálogo de Aleya diaria)
    showDialog(context: context, builder: (context) => const DailyAyahDialog());
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  // بناء عنصر شريط التنقل
  Widget _buildNavItem(int index, String iconName, String label) {
    final isSelected = _selectedIndex == index;

    return GestureDetector(
      onTap: () {
        // تجنب الانتقال إذا كان هناك انتقال آخر قيد التنفيذ
        if (_isPageChanging || _selectedIndex == index) return;

        // تأثير اهتزاز خفيف عند النقر
        HapticFeedback.lightImpact();

        setState(() {
          _selectedIndex = index;
          _isPageChanging = true;
        });

        // الانتقال إلى الصفحة المحددة مباشرة بدون تمرير عبر الصفحات الأخرى
        _pageController.jumpToPage(index);

        // تحديث حالة الانتقال بعد الانتقال مباشرة
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            setState(() {
              _isPageChanging = false;
            });
          }
        });
      },
      child: BottomNavIcon(
        iconName: iconName,
        isSelected: isSelected,
        label: label,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Remove unused variable

    return Scaffold(
      key: _scaffoldKey,
      drawer: const AppDrawer(), // إضافة القائمة الجانبية
      body: IslamicBackground(
        opacity: 0.03,
        showPattern: true,
        showGradient: false,
        child: PageView(
          controller: _pageController,
          physics: const NeverScrollableScrollPhysics(), // منع التمرير الجانبي
          children: _screens,
          onPageChanged: (index) {
            setState(() {
              _selectedIndex = index;
              _isPageChanging = false;
            });
          },
        ),
      ),
      bottomNavigationBar: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
        decoration: BoxDecoration(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context)
                      .scaffoldBackgroundColor // استخدام لون الخلفية الحالي
                  : Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26), // 0.1 * 255 = ~26
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, -2),
            ),
          ],
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNavItem(0, 'mosque', 'الأذكار'),
                _buildNavItem(1, 'quran', 'القرآن'),
                _buildNavItem(2, 'custom_dua', 'دعاء مخصص'),
                _buildNavItem(3, 'menu_dots', 'المزيد'),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
