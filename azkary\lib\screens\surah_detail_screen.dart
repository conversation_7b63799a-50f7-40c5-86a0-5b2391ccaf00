import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/quran_model.dart';
import '../services/quran_provider.dart';
import '../services/theme_provider.dart';
import '../widgets/islamic_background.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/shimmer_loading.dart';
import '../utils/logger.dart';

/// شاشة عرض تفاصيل السورة
class SurahDetailScreen extends StatefulWidget {
  final Surah surah;
  final int? initialAyahNumber; // رقم الآية المراد الانتقال إليها

  const SurahDetailScreen({
    super.key,
    required this.surah,
    this.initialAyahNumber,
  });

  @override
  State<SurahDetailScreen> createState() => _SurahDetailScreenState();
}

class _SurahDetailScreenState extends State<SurahDetailScreen>
    with TickerProviderStateMixin {
  List<Ayah> _ayahs = [];
  bool _isLoading = true;
  String _error = '';

  // تعريف متغير للتحكم في التمرير
  final ScrollController _scrollController = ScrollController();

  // متغيرات الوميض
  late AnimationController _highlightController;
  late Animation<double> _highlightAnimation;
  int? _highlightedAyahNumber;

  // متغيرات وضع عرض الصفحات الجديد
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();

    // تهيئة الوميض
    _highlightController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _highlightAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _highlightController, curve: Curves.easeInOut),
    );

    _loadAyahs();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _highlightController.dispose();
    super.dispose();
  }

  // تم إزالة دوال التنقل بين السور

  /// تحميل آيات السورة
  Future<void> _loadAyahs() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      AppLogger.info(
        'بدء تحميل آيات السورة: ${widget.surah.name} (${widget.surah.number})',
      );
      final quranProvider = Provider.of<QuranProvider>(context, listen: false);

      // محاولة تحميل الآيات مع التفسير مباشرة
      try {
        final ayahsWithTafsir = await quranProvider.getAyahsWithTafsir(
          widget.surah.number,
        );

        if (ayahsWithTafsir.isNotEmpty) {
          AppLogger.info(
            'تم تحميل ${ayahsWithTafsir.length} آية مع التفسير من سورة ${widget.surah.name}',
          );

          // تسجيل معلومات عن أول آية وآخر آية للتشخيص
          if (ayahsWithTafsir.isNotEmpty) {
            final firstAyah = ayahsWithTafsir.first;
            final lastAyah = ayahsWithTafsir.last;

            AppLogger.info(
              'أول آية: رقم=${firstAyah.numberInSurah}, isBismillah=${firstAyah.isBismillah}, النص=${firstAyah.text.substring(0, firstAyah.text.length > 20 ? 20 : firstAyah.text.length)}...',
            );

            AppLogger.info(
              'آخر آية: رقم=${lastAyah.numberInSurah}, النص=${lastAyah.text.substring(0, lastAyah.text.length > 20 ? 20 : lastAyah.text.length)}...',
            );
          }

          if (mounted) {
            setState(() {
              _ayahs = ayahsWithTafsir;
              _isLoading = false;
            });

            // التمرير إلى الآية المحددة بعد تحميل الآيات
            if (widget.initialAyahNumber != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _scrollToAyah(widget.initialAyahNumber!);
              });
            }
          }

          return;
        }
      } catch (tafsirError) {
        AppLogger.warning(
          'خطأ في تحميل الآيات مع التفسير: $tafsirError، محاولة تحميل الآيات فقط',
        );
      }

      // إذا فشل تحميل الآيات مع التفسير، نحاول تحميل الآيات فقط
      final ayahs = await quranProvider.getAyahs(widget.surah.number);

      if (ayahs.isEmpty) {
        AppLogger.warning(
          'لم يتم تحميل أي آيات من السورة ${widget.surah.name}',
        );
        if (mounted) {
          setState(() {
            _error =
                'لم يتم تحميل أي آيات. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.';
            _isLoading = false;
          });
        }
        return;
      }

      AppLogger.info(
        'تم تحميل ${ayahs.length} آية من سورة ${widget.surah.name} (بدون تفسير)',
      );

      // تسجيل معلومات عن أول آية وآخر آية للتشخيص
      if (ayahs.isNotEmpty) {
        final firstAyah = ayahs.first;
        final lastAyah = ayahs.last;

        AppLogger.info(
          'أول آية (بدون تفسير): رقم=${firstAyah.numberInSurah}, isBismillah=${firstAyah.isBismillah}, النص=${firstAyah.text.substring(0, firstAyah.text.length > 20 ? 20 : firstAyah.text.length)}...',
        );

        AppLogger.info(
          'آخر آية (بدون تفسير): رقم=${lastAyah.numberInSurah}, النص=${lastAyah.text.substring(0, lastAyah.text.length > 20 ? 20 : lastAyah.text.length)}...',
        );
      }

      if (mounted) {
        setState(() {
          _ayahs = ayahs;
          _isLoading = false;
        });

        // التمرير إلى الآية المحددة بعد تحميل الآيات
        if (widget.initialAyahNumber != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToAyah(widget.initialAyahNumber!);
          });
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل آيات السورة: $e');
      if (mounted) {
        setState(() {
          _error = 'حدث خطأ أثناء تحميل الآيات: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: widget.surah.name,
        actions: [
          // زر إعدادات الخط
          IconButton(
            tooltip: 'إعدادات الخط',
            icon: Icon(
              Icons.text_fields,
              color: theme.colorScheme.onSurface,
              size: 24,
            ),
            onPressed: () {
              HapticFeedback.lightImpact();
              _showFontSizeDialog();
            },
          ),
          // تم إزالة زر التحديث
        ],
      ),
      body: IslamicBackground(
        opacity: 0.03,
        showPattern: true,
        showGradient: false,
        child: SafeArea(
          child: Column(
            children: [
              // قائمة الآيات
              Expanded(
                child:
                    _isLoading
                        ? _buildLoadingState()
                        : _error.isNotEmpty
                        ? _buildErrorState()
                        : _ayahs.isEmpty
                        ? _buildEmptyState()
                        : _buildAyahsList(),
              ),

              // تم إزالة أزرار التنقل بين السور
            ],
          ),
        ),
      ),
    );
  }

  // تم إزالة دالة _buildSurahInfo() لتوفير مساحة أكبر للعرض

  // تم إزالة الدوال غير المستخدمة

  // تم استبدال هذه الدالة بدالة _buildLineTextSpans

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final baseColor = isDarkMode ? Colors.grey.shade800 : Colors.grey.shade300;

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 10,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: ShimmerLoading(
            isLoading: true,
            child: Container(
              height: 80,
              decoration: BoxDecoration(
                color: baseColor,
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _error,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadAyahs,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة القائمة الفارغة
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.menu_book,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد آيات متاحة حالياً',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'سيتم توفير الآيات قريباً إن شاء الله',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadAyahs,
              icon: const Icon(Icons.refresh),
              label: const Text('تحديث'),
            ),
          ],
        ),
      ),
    );
  }

  /// التمرير إلى آية محددة مع الوميض
  void _scrollToAyah(int ayahNumber) {
    // التأكد من أن الآية موجودة في القائمة
    if (_ayahs.isEmpty || ayahNumber <= 0 || ayahNumber > _ayahs.length) {
      AppLogger.warning('لا يمكن التمرير إلى الآية $ayahNumber: خارج النطاق');
      return;
    }

    // تعيين الآية المراد إبرازها
    setState(() {
      _highlightedAyahNumber = ayahNumber;
    });

    // الحصول على مؤشر الآية في القائمة (الآيات تبدأ من 1 لكن المؤشر يبدأ من 0)
    final index = ayahNumber - 1;

    AppLogger.info('التمرير إلى الآية رقم $ayahNumber (index=$index)');

    // بدء الوميض
    _highlightController.reset();
    _highlightController.forward();

    // تأخير قصير للتأكد من أن القائمة جاهزة للتمرير
    Future.delayed(const Duration(milliseconds: 300), () {
      // التحقق من أن الشاشة لا تزال مثبتة
      if (!mounted) return;

      // التمرير إلى الآية المحددة في وضع عرض الصفحات
      final ayahsPerPage = 13;
      final pageIndex = index ~/ ayahsPerPage;

      AppLogger.info('التمرير إلى الصفحة $pageIndex');

      if (_pageController.hasClients) {
        _pageController.animateToPage(
          pageIndex,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }

      // إيقاف الوميض بعد 3 ثوانٍ
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _highlightedAyahNumber = null;
          });
          _highlightController.reset();
        }
      });
    });
  }

  /// الحصول على الآيات المفلترة للعرض
  List<Ayah> _getFilteredAyahs() {
    // الآيات تأتي معالجة من QuranService، لذا نعيدها مباشرة
    return _ayahs;
  }

  /// بناء قائمة الآيات - وضع عرض الصفحات الجديد
  Widget _buildAyahsList() {
    return _buildPagedView();
  }

  /// بناء وضع عرض الصفحات الجديد
  Widget _buildPagedView() {
    final ayahs = _getFilteredAyahs();
    const int ayahsPerPage = 13; // 13 سطر لكل صفحة

    // تقسيم الآيات إلى صفحات
    final List<List<Ayah>> pages = [];
    for (int i = 0; i < ayahs.length; i += ayahsPerPage) {
      final endIndex =
          (i + ayahsPerPage < ayahs.length) ? i + ayahsPerPage : ayahs.length;
      pages.add(ayahs.sublist(i, endIndex));
    }

    if (pages.isEmpty) {
      return const Center(child: Text('لا توجد آيات'));
    }

    return Column(
      children: [
        // عرض الصفحة الحالية
        Expanded(
          child: PageView.builder(
            controller: _pageController,
            itemCount: pages.length,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            itemBuilder: (context, pageIndex) {
              return _buildSinglePage(
                pages[pageIndex],
                pageIndex + 1,
                pages.length,
              );
            },
          ),
        ),

        // أزرار التنقل ومؤشر الصفحة
        _buildPageNavigation(pages.length),
      ],
    );
  }

  /// بناء صفحة واحدة من الآيات
  Widget _buildSinglePage(
    List<Ayah> pageAyahs,
    int pageNumber,
    int totalPages,
  ) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // رأس الصفحة مع معلومات السورة
          _buildPageHeader(pageNumber, totalPages),

          const SizedBox(height: 16),

          // الآيات في الصفحة
          Expanded(
            child: ListView.builder(
              itemCount: pageAyahs.length,
              itemBuilder: (context, index) {
                final ayah = pageAyahs[index];
                return _buildSimpleAyahCard(ayah, themeProvider.fontSize);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الصفحة
  Widget _buildPageHeader(int pageNumber, int totalPages) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withAlpha(20),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.primary.withAlpha(50),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            widget.surah.name,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          Text(
            'صفحة $pageNumber من $totalPages',
            style: TextStyle(fontSize: 14, color: theme.colorScheme.primary),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة آية مبسطة
  Widget _buildSimpleAyahCard(Ayah ayah, double fontSize) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            theme.brightness == Brightness.dark
                ? theme.cardColor
                : Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              theme.brightness == Brightness.dark
                  ? Colors.grey.withAlpha(30)
                  : Colors.grey.withAlpha(20),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // نص الآية
          Text(
            _cleanArabicText(ayah.text),
            style: TextStyle(
              fontSize: fontSize,
              height: 1.8,
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.right,
            textDirection: TextDirection.rtl,
          ),

          const SizedBox(height: 8),

          // رقم الآية
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withAlpha(20),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${ayah.numberInSurah}',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار التنقل بين الصفحات
  Widget _buildPageNavigation(int totalPages) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        border: Border(top: BorderSide(color: theme.dividerColor, width: 1)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // زر الصفحة السابقة
          ElevatedButton.icon(
            onPressed:
                _currentPage > 0
                    ? () {
                      _pageController.previousPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                    : null,
            icon: const Icon(Icons.arrow_back_ios, size: 16),
            label: const Text('السابق'),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),

          // مؤشر الصفحة الحالية
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(20),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '${_currentPage + 1} / $totalPages',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
          ),

          // زر الصفحة التالية
          ElevatedButton.icon(
            onPressed:
                _currentPage < totalPages - 1
                    ? () {
                      _pageController.nextPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                    : null,
            icon: const Icon(Icons.arrow_forward_ios, size: 16),
            label: const Text('التالي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),
        ],
      ),
    );
  }

  /// تنظيف النص العربي من النقاط السوداء والعلامات المزعجة
  String _cleanArabicText(String text) {
    String cleanedText = text;

    // إزالة النقاط السوداء والعلامات المزعجة
    cleanedText = cleanedText.replaceAll('۟', '');
    cleanedText = cleanedText.replaceAll('۠', '');
    cleanedText = cleanedText.replaceAll('ۡ', '');
    cleanedText = cleanedText.replaceAll('ۢ', '');
    cleanedText = cleanedText.replaceAll('ۣ', '');
    cleanedText = cleanedText.replaceAll('ۤ', '');
    cleanedText = cleanedText.replaceAll('ۥ', '');
    cleanedText = cleanedText.replaceAll('ۦ', '');
    cleanedText = cleanedText.replaceAll('ۧ', '');
    cleanedText = cleanedText.replaceAll('ۨ', '');
    cleanedText = cleanedText.replaceAll('ۭ', '');
    cleanedText = cleanedText.replaceAll('ۮ', '');
    cleanedText = cleanedText.replaceAll('ۯ', '');

    // إزالة المسافات الزائدة
    cleanedText = cleanedText.replaceAll(RegExp(r'\s+'), ' ').trim();

    return cleanedText;
  }

  /// عرض حوار إعدادات الخط
  void _showFontSizeDialog() {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('حجم الخط'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('حجم الخط الحالي: ${themeProvider.fontSize.toInt()}'),
                const SizedBox(height: 16),
                Slider(
                  value: themeProvider.fontSize,
                  min: 14,
                  max: 32,
                  divisions: 18,
                  label: themeProvider.fontSize.toInt().toString(),
                  onChanged: (value) {
                    themeProvider.setFontSize(value);
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  // تم إزالة جميع الدوال غير المستخدمة لتحسين الأداء
}
