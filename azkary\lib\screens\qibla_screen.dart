import 'dart:math' show pi, sin, cos;
import 'package:flutter/material.dart';
import 'package:flutter_qiblah/flutter_qiblah.dart';
import 'package:permission_handler/permission_handler.dart';

/// شاشة بوصلة القبلة
class QiblaScreen extends StatefulWidget {
  const QiblaScreen({super.key});

  @override
  State<QiblaScreen> createState() => _QiblaScreenState();
}

class _QiblaScreenState extends State<QiblaScreen> {
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _checkDeviceSupport();
  }

  /// التحقق من دعم الجهاز لبوصلة القبلة
  Future<void> _checkDeviceSupport() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // التحقق من دعم الجهاز للبوصلة
      final deviceSupport = await Flutter<PERSON><PERSON><PERSON>.androidDeviceSensorSupport();

      if (deviceSupport == true) {
        // التحقق من صلاحيات الموقع
        final permissionStatus = await Permission.location.request();

        if (permissionStatus.isGranted) {
          setState(() {
            _isLoading = false;
          });
        } else {
          setState(() {
            _errorMessage =
                'يرجى السماح بصلاحية الوصول إلى الموقع لاستخدام بوصلة القبلة';
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          _errorMessage = 'جهازك لا يدعم مستشعر البوصلة';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء التحقق من دعم الجهاز: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('بوصلة القبلة'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _checkDeviceSupport,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _errorMessage.isNotEmpty
              ? _buildErrorWidget()
              : _buildQiblaContent(),
    );
  }

  /// بناء محتوى بوصلة القبلة
  Widget _buildQiblaContent() {
    return StreamBuilder<QiblahDirection>(
      stream: FlutterQiblah.qiblahStream,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'حدث خطأ: ${snapshot.error}',
              style: const TextStyle(fontSize: 16, color: Colors.red),
              textAlign: TextAlign.center,
            ),
          );
        }

        if (!snapshot.hasData) {
          return const Center(
            child: Text(
              'لا توجد بيانات متاحة',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          );
        }

        final qiblahDirection = snapshot.data!;
        return _buildQiblahCompass(qiblahDirection);
      },
    );
  }

  /// بناء بوصلة القبلة بطابع إسلامي
  Widget _buildQiblahCompass(QiblahDirection qiblahDirection) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // ألوان إسلامية
    const primaryGreen = Color(0xFF046A38); // أخضر إسلامي
    const secondaryGold = Color(0xFFDAA520); // ذهبي
    const darkBrown = Color(0xFF8B4513); // بني داكن

    // اختيار اللون المناسب حسب السمة
    final compassColor = isDarkMode ? primaryGreen : primaryGreen;

    return Column(
      children: [
        Expanded(
          child: Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                // دائرة البوصلة الخارجية
                Container(
                  width: 300,
                  height: 300,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: theme.cardColor, // استخدام لون البطاقة من الثيم
                    boxShadow: [
                      BoxShadow(
                        color:
                            isDarkMode
                                ? Colors.black.withAlpha(40) // ظل مثل تويتر
                                : Colors.black.withValues(
                                  red: 0,
                                  green: 0,
                                  blue: 0,
                                  alpha: 0.1,
                                ),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ],
                    border: Border.all(
                      color:
                          isDarkMode
                              ? const Color(0x4D9E9E9E) // حدود مثل تويتر
                              : secondaryGold,
                      width: 2,
                    ),
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // علامات الاتجاهات
                      ...List.generate(
                        8,
                        (index) => Transform.rotate(
                          angle: (index * (pi / 4)),
                          child: Align(
                            alignment: const Alignment(0, -0.85),
                            child: Text(
                              _getDirectionText(index),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color:
                                    theme
                                        .colorScheme
                                        .onSurface, // استخدام لون النص من الثيم
                                fontFamily: 'Amiri',
                              ),
                            ),
                          ),
                        ),
                      ),

                      // خط الشمال والبوصلة
                      AnimatedRotation(
                        turns: -qiblahDirection.direction / 360,
                        duration: const Duration(milliseconds: 500),
                        curve: Curves.easeOutCubic,
                        child: SizedBox(
                          width: 280,
                          height: 280,
                          child: CustomPaint(
                            painter: CompassPainter(compassColor),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // صورة الكعبة في المركز
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: secondaryGold, width: 2),
                  ),
                  child: Center(
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.black,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: secondaryGold, width: 1),
                      ),
                      child: Center(
                        child: Container(
                          width: 20,
                          height: 40,
                          decoration: BoxDecoration(
                            color: darkBrown,
                            borderRadius: BorderRadius.circular(2),
                            border: Border.all(
                              color: secondaryGold,
                              width: 0.5,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                // سهم القبلة
                AnimatedRotation(
                  turns: -qiblahDirection.qiblah / 360,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeOutCubic,
                  child: Container(
                    width: 300,
                    height: 300,
                    alignment: Alignment.topCenter,
                    child: Container(
                      width: 30,
                      height: 140,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [primaryGreen, Colors.transparent],
                        ),
                      ),
                      child: Stack(
                        alignment: Alignment.topCenter,
                        children: [
                          // رأس السهم
                          Container(
                            width: 30,
                            height: 30,
                            decoration: BoxDecoration(
                              color: primaryGreen,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: secondaryGold,
                                width: 1,
                              ),
                            ),
                          ),
                          // جسم السهم
                          Positioned(
                            top: 15,
                            child: Container(
                              width: 4,
                              height: 125,
                              color: primaryGreen,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // معلومات الاتجاه بطابع إسلامي
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  red: 0,
                  green: 0,
                  blue: 0,
                  alpha: 0.1,
                ),
                blurRadius: 4,
                spreadRadius: 1,
              ),
            ],
            border: Border.all(color: secondaryGold, width: 1.5),
          ),
          child: Column(
            children: [
              // عنوان إسلامي
              Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: secondaryGold, width: 1),
                  ),
                ),
                child: Center(
                  child: Text(
                    'معلومات اتجاه القبلة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: primaryGreen,
                      fontFamily: 'Amiri',
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // معلومات الاتجاه
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildIslamicDirectionInfo(
                    'اتجاه القبلة',
                    '${qiblahDirection.qiblah.toStringAsFixed(1)}°',
                    Icons.location_on,
                    primaryGreen,
                    secondaryGold,
                  ),
                  _buildIslamicDirectionInfo(
                    'اتجاه البوصلة',
                    '${qiblahDirection.direction.toStringAsFixed(1)}°',
                    Icons.explore,
                    primaryGreen,
                    secondaryGold,
                  ),
                  _buildIslamicDirectionInfo(
                    'دقة المستشعر',
                    'متوسطة',
                    Icons.sensors,
                    primaryGreen,
                    secondaryGold,
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // نصيحة للمستخدم
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFE8F5E9), // Color verde claro
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: primaryGreen, width: 1),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: primaryGreen, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'قم بتحريك هاتفك في دائرة لمعايرة البوصلة',
                        style: TextStyle(
                          fontSize: 14,
                          color: primaryGreen,
                          fontFamily: 'Amiri',
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء معلومات الاتجاه بطابع إسلامي
  Widget _buildIslamicDirectionInfo(
    String label,
    String value,
    IconData icon,
    Color primaryColor,
    Color accentColor,
  ) {
    return Column(
      children: [
        // أيقونة بتصميم إسلامي
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: primaryColor.withAlpha(20), // لون فاتح من اللون الأساسي
            shape: BoxShape.circle,
            border: Border.all(color: accentColor, width: 1),
          ),
          child: Icon(icon, color: primaryColor, size: 24),
        ),
        const SizedBox(height: 8),
        // تسمية
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontFamily: 'Amiri',
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 4),
        // قيمة
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: 'Amiri',
            color: primaryColor,
          ),
        ),
      ],
    );
  }

  /// بناء واجهة الخطأ
  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Theme.of(context).colorScheme.error,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _checkDeviceSupport,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على نص الاتجاه
  String _getDirectionText(int index) {
    const directions = [
      'شمال',
      'شمال شرق',
      'شرق',
      'جنوب شرق',
      'جنوب',
      'جنوب غرب',
      'غرب',
      'شمال غرب',
    ];
    return directions[index];
  }
}

/// رسام البوصلة بطابع إسلامي
class CompassPainter extends CustomPainter {
  final Color color;

  CompassPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // رسم الدائرة الخارجية بنمط إسلامي
    final outerCirclePaint =
        Paint()
          ..color = color
          ..strokeWidth = 3
          ..style = PaintingStyle.stroke;
    canvas.drawCircle(center, radius, outerCirclePaint);

    // رسم الدائرة الداخلية
    final innerCirclePaint =
        Paint()
          ..color = color
          ..strokeWidth = 1.5
          ..style = PaintingStyle.stroke;
    canvas.drawCircle(center, radius * 0.85, innerCirclePaint);

    // رسم الدائرة الثالثة
    final thirdCirclePaint =
        Paint()
          ..color = color
          ..strokeWidth = 1
          ..style = PaintingStyle.stroke;
    canvas.drawCircle(center, radius * 0.7, thirdCirclePaint);

    // رسم خط الشمال
    final northLinePaint =
        Paint()
          ..color = Colors.red
          ..strokeWidth = 3
          ..style = PaintingStyle.stroke;
    canvas.drawLine(
      center,
      Offset(center.dx, center.dy - radius),
      northLinePaint,
    );

    // رسم خطوط الاتجاهات الرئيسية
    final mainDirectionPaint =
        Paint()
          ..color = color
          ..strokeWidth = 2
          ..style = PaintingStyle.stroke;

    // رسم خطوط الاتجاهات الفرعية
    final subDirectionPaint =
        Paint()
          ..color = color
          ..strokeWidth = 1
          ..style = PaintingStyle.stroke;

    // رسم جميع خطوط الاتجاهات
    for (int i = 1; i < 32; i++) {
      final angle = i * (pi / 16);
      final x = center.dx + radius * sin(angle);
      final y = center.dy - radius * cos(angle);

      // تحديد طول الخط حسب نوع الاتجاه
      double lineLength;
      Paint linePaint;

      if (i % 4 == 0) {
        // اتجاهات رئيسية (شمال، جنوب، شرق، غرب)
        lineLength = radius * 0.3;
        linePaint = mainDirectionPaint;
      } else if (i % 2 == 0) {
        // اتجاهات فرعية (شمال شرق، جنوب شرق، إلخ)
        lineLength = radius * 0.2;
        linePaint = subDirectionPaint;
      } else {
        // علامات صغيرة
        lineLength = radius * 0.1;
        linePaint = subDirectionPaint..strokeWidth = 0.5;
      }

      final innerX = center.dx + (radius - lineLength) * sin(angle);
      final innerY = center.dy - (radius - lineLength) * cos(angle);

      canvas.drawLine(Offset(innerX, innerY), Offset(x, y), linePaint);
    }

    // رسم زخارف إسلامية على الدائرة
    final decorPaint =
        Paint()
          ..color = color
          ..strokeWidth = 1
          ..style = PaintingStyle.stroke;

    // رسم أنماط هندسية إسلامية مبسطة
    for (int i = 0; i < 8; i++) {
      final angle = i * (pi / 4);
      final x1 = center.dx + (radius * 0.6) * sin(angle);
      final y1 = center.dy - (radius * 0.6) * cos(angle);

      final x2 = center.dx + (radius * 0.6) * sin(angle + pi / 8);
      final y2 = center.dy - (radius * 0.6) * cos(angle + pi / 8);

      canvas.drawLine(Offset(x1, y1), Offset(x2, y2), decorPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
