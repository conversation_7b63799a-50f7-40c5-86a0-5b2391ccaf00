import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import '../constants/allah_names_data.dart';
import '../widgets/islamic_background.dart';
import '../widgets/custom_app_bar.dart';

/// شاشة عرض أسماء الله الحسنى
class AllahNamesScreen extends StatefulWidget {
  const AllahNamesScreen({super.key});

  @override
  State<AllahNamesScreen> createState() => _AllahNamesScreenState();
}

class _AllahNamesScreenState extends State<AllahNamesScreen>
    with SingleTickerProviderStateMixin {
  // متغير لتخزين الاسم المحدد
  AllahName? _selectedName;

  // متحكم الحركة للتأثيرات
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    // إعداد متحكم الحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInCubic),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: const CustomAppBar(title: 'أسماء الله الحسنى'),
      body: IslamicBackground(
        opacity: 0.03,
        showPattern: true,
        showGradient: false,
        child:
            _selectedName == null
                ? _buildNamesGrid(theme, isDarkMode)
                : _buildNameDetails(theme, isDarkMode),
      ),
    );
  }

  /// بناء شبكة أسماء الله الحسنى
  Widget _buildNamesGrid(ThemeData theme, bool isDarkMode) {
    return Column(
      children: [
        // عنوان توضيحي
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'قال رسول الله ﷺ: "إن لله تسعة وتسعين اسمًا، مائة إلا واحدًا، من أحصاها دخل الجنة"',
            style: TextStyle(
              fontSize: 16,
              fontStyle: FontStyle.italic,
              color: theme.colorScheme.onSurface.withValues(
                red: theme.colorScheme.onSurface.r.toDouble(),
                green: theme.colorScheme.onSurface.g.toDouble(),
                blue: theme.colorScheme.onSurface.b.toDouble(),
                alpha: 0.7,
              ),
            ),
            textAlign: TextAlign.center,
          ),
        ),

        // شبكة الأسماء
        Expanded(
          child: GridView.builder(
            padding: const EdgeInsets.all(16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 1,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: allahNamesData.length,
            itemBuilder: (context, index) {
              final name = allahNamesData[index];
              return _buildNameCard(name, theme, isDarkMode);
            },
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة اسم من أسماء الله الحسنى
  Widget _buildNameCard(AllahName name, ThemeData theme, bool isDarkMode) {
    return Hero(
      tag: 'name-${name.id}',
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            setState(() {
              _selectedName = name;
            });
            _animationController.reset();
            _animationController.forward();
          },
          borderRadius: BorderRadius.circular(16),
          child: Ink(
            decoration: BoxDecoration(
              color: theme.cardColor, // استخدام لون البطاقة من الثيم
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color:
                      isDarkMode
                          ? Colors.black.withAlpha(40) // ظل مثل تويتر
                          : Colors.black.withValues(
                            red: 0,
                            green: 0,
                            blue: 0,
                            alpha: 0.05,
                          ),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color:
                    isDarkMode
                        ? const Color(0x4D9E9E9E) // حدود مثل تويتر
                        : theme.colorScheme.primary.withValues(
                          red: theme.colorScheme.primary.r.toDouble(),
                          green: theme.colorScheme.primary.g.toDouble(),
                          blue: theme.colorScheme.primary.b.toDouble(),
                          alpha: 0.2,
                        ),
                width: 1,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  name.name,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  name.meaning,
                  style: TextStyle(
                    fontSize: 12,
                    color: theme.colorScheme.onSurface.withValues(
                      red: theme.colorScheme.onSurface.r.toDouble(),
                      green: theme.colorScheme.onSurface.g.toDouble(),
                      blue: theme.colorScheme.onSurface.b.toDouble(),
                      alpha: 0.7,
                    ),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء تفاصيل الاسم المحدد
  Widget _buildNameDetails(ThemeData theme, bool isDarkMode) {
    if (_selectedName == null) return const SizedBox.shrink();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _opacityAnimation,
          child: ScaleTransition(scale: _scaleAnimation, child: child),
        );
      },
      child: Stack(
        children: [
          // زر العودة
          Positioned(
            top: 16,
            right: 16,
            child: FloatingActionButton(
              mini: true,
              onPressed: () {
                setState(() {
                  _selectedName = null;
                });
              },
              backgroundColor: theme.cardColor, // استخدام لون البطاقة من الثيم
              child: Icon(
                Icons.arrow_forward,
                color: theme.colorScheme.primary,
              ),
            ),
          ),

          // محتوى التفاصيل
          Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // الاسم بخط كبير
                  Hero(
                    tag: 'name-${_selectedName!.id}',
                    child: Material(
                      color: Colors.transparent,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 24,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withValues(
                            red: theme.colorScheme.primary.r.toDouble(),
                            green: theme.colorScheme.primary.g.toDouble(),
                            blue: theme.colorScheme.primary.b.toDouble(),
                            alpha: 0.1,
                          ),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: theme.colorScheme.primary,
                            width: 2,
                          ),
                        ),
                        child: Text(
                          _selectedName!.name,
                          style: TextStyle(
                            fontSize: 48,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // المعنى
                  Text(
                    _selectedName!.meaning,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 24),

                  // الشرح
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: theme.cardColor, // استخدام لون البطاقة من الثيم
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color:
                              isDarkMode
                                  ? Colors.black.withAlpha(40) // ظل مثل تويتر
                                  : Colors.black.withValues(
                                    red: 0,
                                    green: 0,
                                    blue: 0,
                                    alpha: 0.05,
                                  ),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: Border.all(
                        color:
                            isDarkMode
                                ? const Color(0x4D9E9E9E) // حدود مثل تويتر
                                : Colors.grey.withAlpha(30),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      _selectedName!.description,
                      style: TextStyle(
                        fontSize: 18,
                        height: 1.5,
                        color: theme.colorScheme.onSurface,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  const SizedBox(height: 32),

                  // أزرار المشاركة والنسخ
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // زر النسخ
                      ElevatedButton.icon(
                        onPressed: () {
                          final text =
                              '${_selectedName!.name}: ${_selectedName!.meaning}\n${_selectedName!.description}';
                          Clipboard.setData(ClipboardData(text: text));
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('تم نسخ الاسم ومعناه'),
                              duration: Duration(seconds: 2),
                            ),
                          );
                        },
                        icon: const Icon(Icons.copy),
                        label: const Text('نسخ'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: Colors.white,
                        ),
                      ),

                      const SizedBox(width: 16),

                      // زر المشاركة
                      ElevatedButton.icon(
                        onPressed: () {
                          final text =
                              '${_selectedName!.name}: ${_selectedName!.meaning}\n${_selectedName!.description}';
                          final box = context.findRenderObject() as RenderBox?;
                          SharePlus.instance.share(
                            ShareParams(
                              text: text,
                              sharePositionOrigin:
                                  box != null
                                      ? box.localToGlobal(Offset.zero) &
                                          box.size
                                      : null,
                            ),
                          );
                        },
                        icon: const Icon(Icons.share),
                        label: const Text('مشاركة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
