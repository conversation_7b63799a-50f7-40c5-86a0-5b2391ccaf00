import 'package:flutter/material.dart';
import '../services/azkar_settings_service.dart';

/// ويدجت عداد التسبيح المصغر
/// يعرض زر التسبيح مع العداد الحالي والمطلوب
class MiniZikrCounter extends StatefulWidget {
  final int currentCount;
  final int totalCount;
  final VoidCallback onIncrement;

  const MiniZikrCounter({
    super.key,
    required this.currentCount,
    required this.totalCount,
    required this.onIncrement,
  });

  @override
  State<MiniZikrCounter> createState() => _MiniZikrCounterState();
}

class _MiniZikrCounterState extends State<MiniZikrCounter> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // إذا اكتمل العد، نعرض رسالة الإكمال
    if (widget.currentCount >= widget.totalCount) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.green.withAlpha(20),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.green.withAlpha(60),
            width: 1,
          ),
        ),
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 20,
            ),
            SizedBox(width: 8),
            Text(
              'تم إكمال التسبيح',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
      );
    }

    // إذا لم يكتمل العد، نعرض زر التسبيح البسيط مع العداد
    return Column(
      children: [
        // الزر الرئيسي - تصميم بسيط ومتناسق
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.primary,
                theme.colorScheme.primary.withAlpha(220),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.primary.withAlpha(40),
                blurRadius: 12,
                spreadRadius: 2,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () async {
                // تأثير اهتزاز بسيط فقط
                await AzkarSettingsService.performVibration();

                // تنفيذ زيادة العداد
                widget.onIncrement();
              },
              borderRadius: BorderRadius.circular(20),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // قسم الأيقونة والنص
                    Expanded(
                      child: Row(
                        children: [
                          // أيقونة التسبيح الجميلة
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withAlpha(25),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.white.withAlpha(40),
                                width: 1,
                              ),
                            ),
                            child: const Icon(
                              Icons.touch_app_rounded,
                              size: 20,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 16),

                          // نص التسبيح
                          const Text(
                            'سبح',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // العداد الجميل
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(25),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withAlpha(40),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // رقم العداد الحالي
                          Text(
                            '${widget.currentCount}',
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          // فاصل
                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 8),
                            width: 1,
                            height: 16,
                            color: Colors.white.withAlpha(60),
                          ),
                          // إجمالي العدد المطلوب
                          Text(
                            '${widget.totalCount}',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white.withAlpha(200),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),

        // شريط التقدم الجميل
        const SizedBox(height: 12),
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withAlpha(30),
            borderRadius: BorderRadius.circular(4),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: LinearProgressIndicator(
              value: (widget.currentCount / widget.totalCount).clamp(0.0, 1.0),
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(
                theme.colorScheme.primary.withAlpha(200),
              ),
              minHeight: 8,
            ),
          ),
        ),

        // نص التقدم
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'التقدم',
              style: TextStyle(
                fontSize: 12,
                color: theme.colorScheme.onSurface.withAlpha(150),
              ),
            ),
            Text(
              '${((widget.currentCount / widget.totalCount) * 100).round()}%',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
