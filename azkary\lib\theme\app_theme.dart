import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // الألوان الثابتة - نظام ألوان إسلامي عصري محسن
  static const Color defaultPrimaryColor = Color(
    0xFF1F8A70,
  ); // أخضر زمردي إسلامي
  static const Color defaultSecondaryColor = Color(0xFFD4AF37); // ذهبي إسلامي
  static const Color defaultTertiaryColor = Color(0xFF00838F); // أزرق فيروزي
  static const Color defaultAccentColor = Color(
    0xFF8BC34A,
  ); // أخضر فاتح للتأكيدات
  static const Color defaultSurfaceColor = Color(
    0xFFF8F9FA,
  ); // لون السطح الفاتح

  // ألوان الخلفية
  static const Color lightBackgroundColor = Colors.white; // اللون الأبيض النقي
  static const Color dimBackgroundColor = Color(
    0xFF15202B,
  ); // أزرق داكن مائل للرمادي (مثل تويتر تماماً)
  static const Color darkBackgroundColor = Color(
    0xFF121212,
  ); // أسود مائل للرمادي الداكن للوضع الليلي

  // ألوان البطاقات - محسنة لتكون أفتح وأكثر نعومة
  static const Color lightCardColor = Color(
    0xFFFBFBFB,
  ); // أبيض مائل للرمادي الفاتح جداً
  static const Color lightCardColorSecondary = Color(
    0xFFF8F9FA,
  ); // أبيض مع لمسة رمادية ناعمة
  static const Color dimCardColor = Color(
    0xFF1E2732,
  ); // أزرق داكن أفتح للبطاقات (مثل تويتر تماماً)
  static const Color darkCardColor = Color(
    0xFF1E1E1E,
  ); // رمادي داكن أفتح للبطاقات في الوضع الليلي

  // ألوان النصوص
  static const Color lightTextColor = Color(0xFF212121);
  static const Color dimTextColor = Color(
    0xFFE7E9EA,
  ); // لون نص فاتح مائل للرمادي (مثل تويتر تماماً)
  static const Color darkTextColor = Color(
    0xFFFFFFFF,
  ); // اللون الأبيض لزيادة التباين في الوضع الليلي

  static const Color lightSecondaryTextColor = Color(0xFF757575);
  static const Color dimSecondaryTextColor = Color(
    0xFF8899A6,
  ); // لون نص ثانوي فاتح مائل للرمادي (مثل تويتر تماماً)
  static const Color darkSecondaryTextColor = Color(
    0xFFD0D0D0,
  ); // تفتيح لون النص الثانوي للوضع الليلي

  // ألوان الظلال
  static const Color lightShadowColor = Color(
    0x26000000,
  ); // ظل بشفافية 15% للوضع الفاتح
  static const Color dimShadowColor = Color(
    0x1A000000,
  ); // ظل بشفافية 10% للوضع المعتم
  static const Color darkShadowColor = Color(
    0x1AFFFFFF,
  ); // ظل بشفافية 10% للوضع الليلي

  // ألوان الحالة - تم تحسينها لتكون أكثر وضوحًا
  static const Color successColor = Color(0xFF43A047); // أخضر
  static const Color errorColor = Color(0xFFFF0000); // أحمر نقي واضح
  static const Color warningColor = Color(0xFFFFFF00); // أصفر نقي واضح
  static const Color infoColor = Color(0xFF039BE5); // أزرق
  static const Color brownColor = Color(0xFF8B4513); // بني واضح

  // دالة إنشاء سمة الوضع الفاتح مع دعم تغيير اللون الرئيسي وحجم الخط
  static ThemeData lightTheme({Color? primaryColor, double fontSize = 1.0}) {
    // استخدام اللون المخصص أو اللون الافتراضي
    final Color mainColor = primaryColor ?? defaultPrimaryColor;
    // إنشاء لون ثانوي متناسق مع اللون الرئيسي
    const Color secondaryColor = defaultSecondaryColor;
    const Color tertiaryColor = defaultTertiaryColor;

    return ThemeData(
      useMaterial3: true,
      primaryColor: mainColor,
      primaryColorLight: Color.lerp(mainColor, Colors.white, 0.3)!,
      primaryColorDark: Color.lerp(mainColor, Colors.black, 0.2)!,
      colorScheme: ColorScheme.fromSeed(
        seedColor: mainColor,
        secondary: secondaryColor,
        tertiary: tertiaryColor,
        error: errorColor,
        brightness: Brightness.light,
      ),
      scaffoldBackgroundColor:
          lightBackgroundColor, // استخدام اللون الأبيض النقي كخلفية
      cardColor: lightCardColor, // استخدام اللون الجديد الأفتح
      cardTheme: CardTheme(
        elevation: 2, // ظل أخف للمظهر الناعم
        shadowColor: const Color(0x12000000), // ظل أكثر نعومة وشفافية
        color: lightCardColor, // لون أفتح للبطاقات
        surfaceTintColor: Colors.transparent, // إزالة التأثير اللوني
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20), // زوايا أكثر استدارة
          side: const BorderSide(
            color: Color(0x0F9E9E9E), // برواز أكثر نعومة وشفافية
            width: 0.3,
          ),
        ),
        margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: mainColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(16)),
        ),
      ),
      textTheme: GoogleFonts.cairoTextTheme(
        ThemeData.light().textTheme.copyWith(
          displayLarge: TextStyle(
            color: lightTextColor,
            fontWeight: FontWeight.bold,
            fontSize: 32 * fontSize, // تطبيق معامل حجم الخط
          ),
          displayMedium: TextStyle(
            color: lightTextColor,
            fontWeight: FontWeight.bold,
            fontSize: 28 * fontSize, // تطبيق معامل حجم الخط
          ),
          displaySmall: TextStyle(
            color: lightTextColor,
            fontWeight: FontWeight.bold,
            fontSize: 24 * fontSize, // تطبيق معامل حجم الخط
          ),
          headlineMedium: TextStyle(
            color: lightTextColor,
            fontWeight: FontWeight.bold,
            fontSize: 20 * fontSize, // تطبيق معامل حجم الخط
          ),
          headlineSmall: TextStyle(
            color: lightTextColor,
            fontWeight: FontWeight.w600,
            fontSize: 18 * fontSize, // تطبيق معامل حجم الخط
          ),
          titleLarge: TextStyle(
            color: lightTextColor,
            fontWeight: FontWeight.w600,
            fontSize: 16 * fontSize, // تطبيق معامل حجم الخط
          ),
          bodyLarge: TextStyle(
            color: lightTextColor,
            fontSize: 16 * fontSize, // تطبيق معامل حجم الخط
          ),
          bodyMedium: TextStyle(
            color: lightTextColor,
            fontSize: 14 * fontSize, // تطبيق معامل حجم الخط
          ),
        ),
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: Colors.white, // لون ثابت لشريط التنقل السفلي
        elevation: 8, // زيادة الظل
        selectedItemColor: mainColor, // استخدام اللون الرئيسي للعناصر المحددة
        unselectedItemColor: const Color(
          0xFF757575,
        ), // لون ثابت للعناصر غير المحددة
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: mainColor,
        foregroundColor: Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: mainColor,
          foregroundColor: Colors.white,
          elevation: 2,
          shadowColor: lightShadowColor,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: mainColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: mainColor,
          side: BorderSide(color: mainColor, width: 1.5),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      iconButtonTheme: IconButtonThemeData(
        style: IconButton.styleFrom(
          foregroundColor: mainColor,
          backgroundColor: Color.lerp(mainColor, Colors.white, 0.85),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.white, // تأكيد أن لون خلفية المدخلات أبيض
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Color.lerp(mainColor, Colors.white, 0.5)!,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Color.lerp(mainColor, Colors.white, 0.5)!,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: mainColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor, width: 2),
        ),
      ),
    );
  }

  // دالة إنشاء سمة الوضع المعتم (Dim Mode) مع دعم تغيير اللون الرئيسي وحجم الخط
  static ThemeData dimTheme({Color? primaryColor, double fontSize = 1.0}) {
    // استخدام اللون المخصص أو اللون الافتراضي
    final Color mainColor = primaryColor ?? defaultPrimaryColor;
    // إنشاء لون ثانوي متناسق مع اللون الرئيسي
    const Color secondaryColor = defaultSecondaryColor;
    const Color tertiaryColor = defaultTertiaryColor;

    return ThemeData(
      useMaterial3: true,
      primaryColor: mainColor,
      primaryColorLight: Color.lerp(mainColor, Colors.white, 0.3)!,
      primaryColorDark: Color.lerp(mainColor, Colors.black, 0.2)!,
      colorScheme: ColorScheme.fromSeed(
        seedColor: mainColor,
        secondary: secondaryColor,
        tertiary: tertiaryColor,
        error: errorColor,
        brightness: Brightness.dark,
      ),
      scaffoldBackgroundColor: dimBackgroundColor,
      cardColor: dimCardColor,
      cardTheme: CardTheme(
        elevation: 2, // ظل أقل في الوضع المعتم
        shadowColor: const Color(0x40000000), // ظل أكثر وضوحاً
        color: dimCardColor,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: const BorderSide(
            color: Color(0x26FFFFFF), // حدود فاتحة للوضع المعتم
            width: 0.5,
          ),
        ),
        margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: dimBackgroundColor, // نفس لون الخلفية مثل تويتر
        foregroundColor: dimTextColor,
        elevation: 0, // بدون ظل مثل تويتر
        centerTitle: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(16)),
        ),
      ),
      textTheme: GoogleFonts.cairoTextTheme(
        ThemeData.dark().textTheme.copyWith(
          displayLarge: TextStyle(
            color: dimTextColor,
            fontWeight: FontWeight.bold,
            fontSize: 32 * fontSize,
          ),
          displayMedium: TextStyle(
            color: dimTextColor,
            fontWeight: FontWeight.bold,
            fontSize: 28 * fontSize,
          ),
          displaySmall: TextStyle(
            color: dimTextColor,
            fontWeight: FontWeight.bold,
            fontSize: 24 * fontSize,
          ),
          headlineMedium: TextStyle(
            color: dimTextColor,
            fontWeight: FontWeight.bold,
            fontSize: 20 * fontSize,
          ),
          headlineSmall: TextStyle(
            color: dimTextColor,
            fontWeight: FontWeight.w600,
            fontSize: 18 * fontSize,
          ),
          titleLarge: TextStyle(
            color: dimTextColor,
            fontWeight: FontWeight.w600,
            fontSize: 16 * fontSize,
          ),
          bodyLarge: TextStyle(color: dimTextColor, fontSize: 16 * fontSize),
          bodyMedium: TextStyle(color: dimTextColor, fontSize: 14 * fontSize),
        ),
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: dimBackgroundColor, // نفس لون الخلفية مثل تويتر
        elevation: 8,
        selectedItemColor: mainColor,
        unselectedItemColor: dimSecondaryTextColor,
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: mainColor,
        foregroundColor: Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: mainColor,
          foregroundColor: Colors.white,
          elevation: 2,
          shadowColor: dimShadowColor,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: mainColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: mainColor,
          side: BorderSide(color: mainColor, width: 1.5),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      iconButtonTheme: IconButtonThemeData(
        style: IconButton.styleFrom(
          foregroundColor: mainColor,
          backgroundColor: Color.lerp(mainColor, dimBackgroundColor, 0.85),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: dimCardColor,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Color.lerp(mainColor, dimBackgroundColor, 0.5)!,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Color.lerp(mainColor, dimBackgroundColor, 0.5)!,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: mainColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor, width: 2),
        ),
      ),
    );
  }

  // دالة إنشاء سمة الوضع الليلي (Dark Mode) مع دعم تغيير اللون الرئيسي وحجم الخط
  static ThemeData darkTheme({Color? primaryColor, double fontSize = 1.0}) {
    // استخدام اللون المخصص أو اللون الافتراضي
    final Color mainColor = primaryColor ?? defaultPrimaryColor;
    // إنشاء لون ثانوي متناسق مع اللون الرئيسي
    const Color secondaryColor = defaultSecondaryColor;
    const Color tertiaryColor = defaultTertiaryColor;

    return ThemeData(
      useMaterial3: true,
      primaryColor: mainColor,
      primaryColorLight: Color.lerp(mainColor, Colors.white, 0.3)!,
      primaryColorDark: Color.lerp(mainColor, Colors.black, 0.2)!,
      colorScheme: ColorScheme.fromSeed(
        seedColor: mainColor,
        secondary: secondaryColor,
        tertiary: tertiaryColor,
        error: errorColor,
        brightness: Brightness.dark,
      ),
      scaffoldBackgroundColor: darkBackgroundColor,
      cardColor:
          darkCardColor, // تعيين لون البطاقات الافتراضي إلى اللون الداكن في الوضع الداكن
      cardTheme: CardTheme(
        elevation: 2, // ظل أقل في الوضع الداكن
        shadowColor: const Color(0x60000000), // ظل أكثر وضوحاً
        color: darkCardColor, // لون داكن للبطاقات في الوضع الداكن
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: const BorderSide(
            color: Color(0x1AFFFFFF), // حدود فاتحة للوضع الداكن
            width: 0.5,
          ),
        ),
        margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF2C2C2C), // لون مختلف عن الوضع المعتم
        foregroundColor: darkTextColor,
        elevation: 4, // ظل أقوى للوضع الليلي
        centerTitle: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(16)),
        ),
      ),
      textTheme: GoogleFonts.cairoTextTheme(
        ThemeData.dark().textTheme.copyWith(
          displayLarge: TextStyle(
            color: darkTextColor,
            fontWeight: FontWeight.bold,
            fontSize: 32 * fontSize, // تطبيق معامل حجم الخط
          ),
          displayMedium: TextStyle(
            color: darkTextColor,
            fontWeight: FontWeight.bold,
            fontSize: 28 * fontSize, // تطبيق معامل حجم الخط
          ),
          displaySmall: TextStyle(
            color: darkTextColor,
            fontWeight: FontWeight.bold,
            fontSize: 24 * fontSize, // تطبيق معامل حجم الخط
          ),
          headlineMedium: TextStyle(
            color: darkTextColor,
            fontWeight: FontWeight.bold,
            fontSize: 20 * fontSize, // تطبيق معامل حجم الخط
          ),
          headlineSmall: TextStyle(
            color: darkTextColor,
            fontWeight: FontWeight.w600,
            fontSize: 18 * fontSize, // تطبيق معامل حجم الخط
          ),
          titleLarge: TextStyle(
            color: darkTextColor,
            fontWeight: FontWeight.w600,
            fontSize: 16 * fontSize, // تطبيق معامل حجم الخط
          ),
          bodyLarge: TextStyle(
            color: darkTextColor,
            fontSize: 16 * fontSize, // تطبيق معامل حجم الخط
          ),
          bodyMedium: TextStyle(
            color: darkTextColor,
            fontSize: 14 * fontSize, // تطبيق معامل حجم الخط
          ),
        ),
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: const Color(
          0xFF2C2C2C,
        ), // نفس لون شريط التطبيق للتناسق في الوضع الليلي
        elevation: 8, // زيادة الظل
        selectedItemColor: mainColor, // استخدام اللون الرئيسي للعناصر المحددة
        unselectedItemColor: const Color(
          0xFFD0D0D0,
        ), // لون ثابت للعناصر غير المحددة
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: mainColor,
        foregroundColor: Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: mainColor,
          foregroundColor: Colors.white,
          elevation: 2,
          shadowColor: darkShadowColor,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: mainColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: mainColor,
          side: BorderSide(color: mainColor, width: 1.5),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      iconButtonTheme: IconButtonThemeData(
        style: IconButton.styleFrom(
          foregroundColor: mainColor,
          backgroundColor: Color.lerp(mainColor, Colors.black, 0.85),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: darkCardColor,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Color.lerp(mainColor, Colors.black, 0.5)!,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Color.lerp(mainColor, Colors.black, 0.5)!,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: mainColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor, width: 2),
        ),
      ),
    );
  }
}
