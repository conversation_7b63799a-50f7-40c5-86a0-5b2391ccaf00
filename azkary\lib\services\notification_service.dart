import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../utils/platform_helper.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  factory NotificationService() {
    return _instance;
  }

  NotificationService._internal();

  Future<void> init() async {
    try {
      // التحقق من دعم المنصة للإشعارات
      if (kIsWeb) {
        // الويب لا يدعم الإشعارات المحلية بنفس الطريقة
        debugPrint('Web platform does not support local notifications in the same way');
        return;
      }

      // التحقق من المنصة بطريقة آمنة
      bool isMobile = PlatformHelper.isMobile;
      if (!isMobile) {
        // قد لا تعمل الإشعارات بشكل صحيح على المنصات غير المحمولة
        debugPrint('Notifications may not work properly on non-mobile platforms');
      }

      // تهيئة المناطق الزمنية
      tz.initializeTimeZones();

      // تعيين المنطقة الزمنية المحلية (يمكن تعديلها حسب البلد)
      tz.setLocalLocation(tz.getLocation('Asia/Riyadh'));

      // إعدادات تهيئة الإشعارات للأندرويد
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // إعدادات تهيئة الإشعارات لجميع المنصات
      const InitializationSettings initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid,
      );

      // تهيئة مكتبة الإشعارات
      await flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: (NotificationResponse response) {
          // يمكن إضافة معالجة النقر على الإشعار هنا
        },
      );
    } catch (e) {
      // خطأ في تهيئة الإشعارات
      debugPrint('Error initializing notifications: $e');
    }
  }

  // تبسيط دالة جدولة الإشعارات
  Future<void> _scheduleNotification({
    required int id,
    required String title,
    required String body,
    required int hour,
    required int minute,
    required String channelId,
    required String channelName,
    required String channelDescription,
  }) async {
    try {
      final tz.TZDateTime scheduledTime = _nextInstanceOfTime(hour, minute);

      await flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        scheduledTime,
        NotificationDetails(
          android: AndroidNotificationDetails(
            channelId,
            channelName,
            channelDescription: channelDescription,
            importance: Importance.high,
            priority: Priority.high,
          ),
        ),
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        matchDateTimeComponents: DateTimeComponents.time,
      );
    } catch (e) {
      debugPrint('Error scheduling notification: $e');
    }
  }

  Future<void> scheduleMorningAzkar() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      bool isEnabled = prefs.getBool('morning_azkar_enabled') ?? false;
      if (!isEnabled) return;

      int hour = prefs.getInt('morning_azkar_hour') ?? 7;
      int minute = prefs.getInt('morning_azkar_minute') ?? 0;

      await _scheduleNotification(
        id: 1,
        title: 'أذكار الصباح',
        body: 'حان وقت أذكار الصباح، لا تنسى ذكر الله',
        hour: hour,
        minute: minute,
        channelId: 'morning_azkar_channel',
        channelName: 'أذكار الصباح',
        channelDescription: 'تنبيهات أذكار الصباح',
      );
    } catch (e) {
      debugPrint('Error scheduling morning azkar: $e');
    }
  }

  Future<void> scheduleEveningAzkar() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      bool isEnabled = prefs.getBool('evening_azkar_enabled') ?? false;
      if (!isEnabled) return;

      int hour = prefs.getInt('evening_azkar_hour') ?? 17;
      int minute = prefs.getInt('evening_azkar_minute') ?? 0;

      await _scheduleNotification(
        id: 2,
        title: 'أذكار المساء',
        body: 'حان وقت أذكار المساء، لا تنسى ذكر الله',
        hour: hour,
        minute: minute,
        channelId: 'evening_azkar_channel',
        channelName: 'أذكار المساء',
        channelDescription: 'تنبيهات أذكار المساء',
      );
    } catch (e) {
      debugPrint('Error scheduling evening azkar: $e');
    }
  }

  Future<void> scheduleDailyZikr() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      bool isEnabled = prefs.getBool('daily_zikr_enabled') ?? false;
      if (!isEnabled) return;

      int hour = prefs.getInt('daily_zikr_hour') ?? 12;
      int minute = prefs.getInt('daily_zikr_minute') ?? 0;

      await _scheduleNotification(
        id: 3,
        title: 'ذكر اليوم',
        body: 'تذكير بذكر اليوم، افتح التطبيق للاطلاع عليه',
        hour: hour,
        minute: minute,
        channelId: 'daily_zikr_channel',
        channelName: 'ذكر اليوم',
        channelDescription: 'تنبيهات ذكر اليوم',
      );
    } catch (e) {
      debugPrint('Error scheduling daily zikr: $e');
    }
  }

  tz.TZDateTime _nextInstanceOfTime(int hour, int minute) {
    // الحصول على الوقت الحالي بالمنطقة الزمنية المحلية
    final tz.TZDateTime now = tz.TZDateTime.now(tz.local);

    // إنشاء وقت الجدولة باستخدام التاريخ الحالي والساعة والدقيقة المحددة
    tz.TZDateTime scheduledDate =
        tz.TZDateTime(tz.local, now.year, now.month, now.day, hour, minute);

    // إذا كان الوقت المجدول قبل الوقت الحالي، أضف يوم واحد
    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }

    return scheduledDate;
  }

  Future<void> cancelAllNotifications() async {
    try {
      await flutterLocalNotificationsPlugin.cancelAll();
    } catch (e) {
      debugPrint('Error canceling notifications: $e');
    }
  }
}
