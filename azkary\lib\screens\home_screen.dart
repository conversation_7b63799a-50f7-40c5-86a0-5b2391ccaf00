import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';

import '../services/azkar_provider.dart';
import '../services/feedback_provider.dart';
import '../models/azkar_model.dart';
import '../utils/logger.dart';
import 'azkar_list_screen.dart';
import 'custom_azkar_screen.dart';
import 'settings_screen.dart';
// removed unused import
import '../widgets/daily_allah_name.dart';
// removed unused import
import '../widgets/prayer_times_bar.dart';
import '../widgets/islamic_background.dart';
import '../services/prayer_provider.dart';
import '../services/daily_question_service.dart';
import '../utils/page_transitions.dart';
import '../utils/color_extensions.dart';
import '../widgets/animated_question_icon.dart';
import '../models/daily_question.dart';
import 'daily_question_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // التأكد من تحميل البيانات
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final provider = Provider.of<AzkarProvider>(context, listen: false);
      AppLogger.info(
        'Home screen initialized, categories count: ${provider.categories.length}',
      );

      // إذا كانت التصنيفات فارغة، قم بإعادة تحميل البيانات
      if (provider.categories.isEmpty) {
        AppLogger.info('Categories are empty, reloading data...');
        await _reloadData();
      } else {
        AppLogger.info('Categories already loaded:');
        for (var category in provider.categories) {
          AppLogger.info('- ${category.name} (${category.icon})');
        }
      }

      // تهيئة مزود أوقات الصلاة
      if (mounted) {
        final prayerProvider = Provider.of<PrayerProvider>(
          context,
          listen: false,
        );
        prayerProvider.initialize();
      }
    });
  }

  // دالة لإعادة تحميل البيانات
  Future<void> _reloadData() async {
    try {
      AppLogger.info('Reloading data from home screen');
      final provider = Provider.of<AzkarProvider>(context, listen: false);

      // إعادة تهيئة قاعدة البيانات وتحميل البيانات
      await provider.reloadData();

      AppLogger.info(
        'Data reloaded, categories count: ${provider.categories.length}',
      );
      if (provider.categories.isNotEmpty) {
        AppLogger.info('Categories loaded successfully:');
        for (var category in provider.categories) {
          AppLogger.info('- ${category.name} (${category.icon})');
        }
      } else {
        AppLogger.warning(
          'No categories loaded after reload, forcing another reload',
        );
        // محاولة إعادة التحميل مرة أخرى
        await provider.reloadData();
      }

      // تحديث واجهة المستخدم
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      AppLogger.error('Error reloading data from home screen: $e');
      // محاولة إعادة التحميل في حالة الخطأ
      if (mounted) {
        final provider = Provider.of<AzkarProvider>(context, listen: false);
        // محاولة إعادة التحميل مرة أخرى
        await provider.reloadData();
      }

      if (mounted) {
        setState(() {});
      }
    }
  }

  // دالة لتنسيق التاريخ بالعربية
  String _getFormattedDate() {
    final now = DateTime.now();
    final formatter = DateFormat('EEEE، d MMMM yyyy', 'ar');
    return formatter.format(now);
  }

  // بناء بطاقة السؤال اليومي
  Widget _buildDailyQuestionCard() {
    final theme = Theme.of(context);

    return Selector<
      DailyQuestionService,
      ({bool hasCompleted, int answered, int total, QuestionStats stats})
    >(
      selector:
          (context, service) => (
            hasCompleted: service.hasCompletedAllToday,
            answered: service.answeredQuestionsToday,
            total: service.totalDailyQuestions,
            stats: service.stats,
          ),
      builder: (context, data, child) {
        // إذا تم الإجابة على جميع الأسئلة، لا نعرض البطاقة (ستظهر الأيقونة في شريط التطبيق فقط)
        if (data.hasCompleted) {
          return const SizedBox.shrink();
        }

        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: Container(
            key: ValueKey(data.hasCompleted),
            margin: const EdgeInsets.all(16),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  Navigator.of(context).push(
                    PageTransitions.slideFromRight(const DailyQuestionScreen()),
                  );
                },
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.primaryWithOpacity(
                          theme.colorScheme.primary,
                          0.15,
                        ),
                        AppColors.primaryWithOpacity(
                          theme.colorScheme.primary,
                          0.08,
                        ),
                        AppColors.primaryWithOpacity(
                          theme.colorScheme.primary,
                          0.05,
                        ),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: AppColors.primaryWithOpacity(
                        theme.colorScheme.primary,
                        0.3,
                      ),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color:
                            theme.brightness == Brightness.dark
                                ? AppColors.blackWithOpacity(0.4)
                                : AppColors.primaryWithOpacity(
                                  theme.colorScheme.primary,
                                  0.2,
                                ),
                        blurRadius: 15,
                        spreadRadius: 2,
                        offset: const Offset(0, 5),
                      ),
                      BoxShadow(
                        color: AppColors.primaryWithOpacity(
                          theme.colorScheme.primary,
                          0.1,
                        ),
                        blurRadius: 30,
                        spreadRadius: 5,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      // أيقونة السؤال المتحركة
                      Stack(
                        children: [
                          AnimatedQuestionIcon(
                            isAnswered: data.hasCompleted,
                            size: 70,
                            onTap: () {
                              Navigator.of(context).push(
                                PageTransitions.slideFromRight(
                                  const DailyQuestionScreen(),
                                ),
                              );
                            },
                          ),
                          if (data.answered > 0)
                            Positioned(
                              top: 5,
                              right: 5,
                              child: Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: Colors.orange,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 2,
                                  ),
                                ),
                                child: Text(
                                  '${data.answered}/${data.total}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),

                      const SizedBox(width: 20),

                      // محتوى البطاقة
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  'الأسئلة اليومية',
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.orangeWithOpacity(0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '${data.answered}/${data.total}',
                                    style: const TextStyle(
                                      fontSize: 10,
                                      color: Colors.orange,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'اختبر معلوماتك الإسلامية مع 3 أسئلة جديدة كل يوم 🧠',
                              style: TextStyle(
                                fontSize: 15,
                                color: AppColors.primaryWithOpacity(
                                  theme.colorScheme.onSurface,
                                  0.8,
                                ),
                                height: 1.4,
                              ),
                            ),
                            const SizedBox(height: 12),
                            if (data.stats.totalQuestions > 0)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.primaryWithOpacity(
                                    theme.colorScheme.primary,
                                    0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                  border: Border.all(
                                    color: AppColors.primaryWithOpacity(
                                      theme.colorScheme.primary,
                                      0.2,
                                    ),
                                  ),
                                ),
                                child: Text(
                                  'دقتك: ${data.stats.accuracy.toStringAsFixed(1)}% • السلسلة: ${data.stats.currentStreak} 🏆',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: theme.colorScheme.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),

                      // سهم الانتقال مع تأثير
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primaryWithOpacity(
                            theme.colorScheme.primary,
                            0.1,
                          ),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_forward_ios,
                          color: theme.colorScheme.primary,
                          size: 18,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('أذكاري'),
        actions: [
          // أيقونة السؤال اليومي المتحركة
          Selector<
            DailyQuestionService,
            ({bool hasCompleted, int answered, int total})
          >(
            selector:
                (context, service) => (
                  hasCompleted: service.hasCompletedAllToday,
                  answered: service.answeredQuestionsToday,
                  total: service.totalDailyQuestions,
                ),
            builder: (context, data, child) {
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: Stack(
                  children: [
                    AnimatedQuestionIcon(
                      isAnswered: data.hasCompleted,
                      showMiniVersion: true,
                      onTap: () {
                        Navigator.of(context).push(
                          PageTransitions.slideFromRight(
                            const DailyQuestionScreen(),
                          ),
                        );
                      },
                    ),
                    if (!data.hasCompleted && data.answered > 0)
                      Positioned(
                        top: -2,
                        right: -2,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.orange,
                            shape: BoxShape.circle,
                          ),
                          child: Text(
                            '${data.answered}/${data.total}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 8,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                PageTransitions.slideFromRight(const SettingsScreen()),
              );
            },
          ),
        ],
      ),
      body: IslamicBackground(
        opacity: theme.brightness == Brightness.dark ? 0.02 : 0.03,
        showPattern: true,
        showGradient: false,
        child: Selector<
          AzkarProvider,
          ({
            bool isLoading,
            List<Category> categories,
            Zikr? dailyZikr,
            List<Zikr> customAzkar,
          })
        >(
          selector:
              (context, provider) => (
                isLoading: provider.isLoading,
                categories: provider.categories,
                dailyZikr: provider.dailyZikr,
                customAzkar: provider.customAzkar,
              ),
          builder: (context, data, child) {
            if (data.isLoading) {
              return const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                ),
              );
            }

            // التحقق من وجود البيانات
            if (data.categories.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'لا توجد بيانات متاحة',
                      style: TextStyle(fontSize: 18),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _reloadData,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('إعادة تحميل البيانات'),
                    ),
                  ],
                ),
              );
            }

            return SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // شريط أوقات الصلاة
                  const PrayerTimesBar(),

                  // بطاقة السؤال اليومي
                  _buildDailyQuestionCard(),

                  // قسم ذكر اليوم
                  if (data.dailyZikr != null)
                    _buildDailyZikrSection(data.dailyZikr!),

                  // قسم تصنيفات الأذكار
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'تصنيفات الأذكار',
                      style: TextStyle(
                        fontSize: 18, // تقليل حجم الخط للدقة 1080p
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  // شبكة التصنيفات المبسطة
                  _buildSimpleCategoriesGrid(data.categories),

                  // قسم الأذكار الخاصة
                  _buildCustomAzkarSection(data.customAzkar, theme),

                  // قسم اسم الله اليومي
                  const Padding(
                    padding: EdgeInsets.fromLTRB(16, 24, 16, 8),
                    child: Text(
                      'اسم الله اليومي',
                      style: TextStyle(
                        fontSize: 18, // تقليل حجم الخط للدقة 1080p
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  // مكون اسم الله اليومي
                  const DailyAllahName(),

                  // مساحة إضافية في النهاية
                  const SizedBox(height: 20),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  // بناء قسم ذكر اليوم المحسن والمتجاوب
  Widget _buildDailyZikrSection(Zikr dailyZikr) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    // تحديد الأحجام والمسافات حسب حجم الشاشة
    final double margin = isSmallScreen ? 12 : 16;
    final double padding = isSmallScreen ? 14 : 16;
    final double borderRadius = isSmallScreen ? 16 : 20;
    final double titleSize = isSmallScreen ? 16 : 18;
    final double textSize = isSmallScreen ? 15 : 16;
    final double sourceSize = isSmallScreen ? 11 : 12;
    final double dateSize = isSmallScreen ? 10 : 12;

    return Hero(
      tag: 'daily-zikr',
      child: Container(
        margin: EdgeInsets.all(margin),
        padding: EdgeInsets.all(padding),
        width: double.infinity,
        decoration: BoxDecoration(
          color:
              theme.brightness == Brightness.dark
                  ? theme.cardColor
                  : Colors.white,
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(
            color:
                theme.brightness == Brightness.dark
                    ? Colors.grey.withAlpha(30)
                    : Colors.grey.withAlpha(20),
            width: 1.0,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(8),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // العنوان والتاريخ المحسن
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 10 : 12,
                      vertical: isSmallScreen ? 6 : 8,
                    ),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          theme.colorScheme.primary.withAlpha(30),
                          theme.colorScheme.primary.withAlpha(15),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(
                        isSmallScreen ? 16 : 20,
                      ),
                      border: Border.all(
                        color: theme.colorScheme.primary.withAlpha(40),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.auto_awesome,
                          color:
                              theme.brightness == Brightness.dark
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                          size: isSmallScreen ? 16 : 18,
                        ),
                        SizedBox(width: isSmallScreen ? 6 : 8),
                        Flexible(
                          child: Text(
                            'ذكري اليومي',
                            style: TextStyle(
                              fontSize: titleSize,
                              fontWeight: FontWeight.bold,
                              color:
                                  theme.brightness == Brightness.dark
                                      ? Colors.grey.shade300
                                      : Colors.grey.shade700,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 8 : 12),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.calendar_today,
                      color:
                          theme.brightness == Brightness.dark
                              ? Colors.grey.shade400
                              : Colors.grey.shade600,
                      size: isSmallScreen ? 10 : 12,
                    ),
                    SizedBox(width: isSmallScreen ? 3 : 4),
                    Text(
                      _getFormattedDate(),
                      style: TextStyle(
                        fontSize: dateSize,
                        fontWeight: FontWeight.bold,
                        color:
                            theme.brightness == Brightness.dark
                                ? Colors.grey.shade400
                                : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 14 : 16),

            // نص الذكر مع تحسينات
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface.withAlpha(50),
                borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                border: Border.all(
                  color: theme.colorScheme.outline.withAlpha(20),
                  width: 1,
                ),
              ),
              child: Text(
                dailyZikr.text,
                style: TextStyle(
                  fontSize: textSize,
                  height: 1.6,
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                softWrap: true,
                overflow: TextOverflow.visible,
              ),
            ),
            SizedBox(height: isSmallScreen ? 10 : 12),

            // المصدر والأزرار
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 8 : 10,
                      vertical: isSmallScreen ? 4 : 6,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.menu_book,
                          color:
                              theme.brightness == Brightness.dark
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                          size: isSmallScreen ? 12 : 14,
                        ),
                        SizedBox(width: isSmallScreen ? 4 : 6),
                        Flexible(
                          child: Text(
                            'رواه ${dailyZikr.source}',
                            style: TextStyle(
                              fontSize: sourceSize,
                              fontWeight: FontWeight.bold,
                              color:
                                  theme.brightness == Brightness.dark
                                      ? Colors.grey.shade400
                                      : Colors.grey.shade600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 8 : 12),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildModernActionButton(
                      Icons.copy_outlined,
                      'نسخ',
                      theme.colorScheme.primary,
                      isSmallScreen,
                      onPressed: () => _copyToClipboard(dailyZikr.text),
                    ),
                    SizedBox(width: isSmallScreen ? 8 : 12),
                    _buildModernActionButton(
                      Icons.share_outlined,
                      'مشاركة',
                      theme.colorScheme.primary,
                      isSmallScreen,
                      onPressed: () => _shareZikr(dailyZikr),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء زر عمل حديث ومحسن
  Widget _buildModernActionButton(
    IconData icon,
    String tooltip,
    Color color,
    bool isSmallScreen, {
    VoidCallback? onPressed,
  }) {
    final size = isSmallScreen ? 32.0 : 36.0;
    final iconSize = isSmallScreen ? 16.0 : 18.0;

    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(size / 2),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [color.withAlpha(30), color.withAlpha(15)],
          ),
          shape: BoxShape.circle,
          border: Border.all(color: color.withAlpha(60), width: 1),
          boxShadow: [
            BoxShadow(
              color: color.withAlpha(20),
              blurRadius: 4,
              spreadRadius: 1,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Tooltip(
          message: tooltip,
          child: Icon(icon, size: iconSize, color: color),
        ),
      ),
    );
  }

  // نسخ النص إلى الحافظة
  void _copyToClipboard(String text) {
    // تنفيذ نسخ النص إلى الحافظة
    Clipboard.setData(ClipboardData(text: text));

    // تنفيذ اهتزاز خفيف
    final feedbackProvider = Provider.of<FeedbackProvider>(
      context,
      listen: false,
    );
    feedbackProvider.lightHapticFeedback();

    // عرض رسالة تأكيد
    feedbackProvider.showSuccessSnackBar(
      context,
      'تم نسخ النص',
      icon: Icons.check_circle,
    );
  }

  // مشاركة الذكر
  void _shareZikr(Zikr zikr) {
    // تنفيذ مشاركة الذكر
    final text = '${zikr.text}\n\nرواه ${zikr.source}\n\nمن تطبيق أذكاري';

    // استخدام SharePlus بالطريقة الصحيحة
    final box = context.findRenderObject() as RenderBox?;
    SharePlus.instance.share(
      ShareParams(
        text: text,
        sharePositionOrigin:
            box != null ? box.localToGlobal(Offset.zero) & box.size : null,
      ),
    );

    // تنفيذ اهتزاز خفيف
    final feedbackProvider = Provider.of<FeedbackProvider>(
      context,
      listen: false,
    );
    feedbackProvider.lightHapticFeedback();

    // عرض رسالة تأكيد
    feedbackProvider.showInfoSnackBar(context, 'تمت مشاركة الذكر');
  }

  // بناء شبكة التصنيفات المبسطة - محسنة للأداء
  Widget _buildSimpleCategoriesGrid(List<Category> categories) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;

    // تبسيط عدد الأعمدة
    int crossAxisCount = isSmallScreen ? 2 : 3;
    double childAspectRatio = 1.0;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 16),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: childAspectRatio,
        ),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          return _buildSimpleCategoryItem(categories[index]);
        },
      ),
    );
  }

  // بناء عنصر التصنيف المبسط - محسن للأداء
  Widget _buildSimpleCategoryItem(Category category) {
    final theme = Theme.of(context);

    // تبسيط الأحجام للدقة 1080p
    const double iconSize = 48;
    const double iconInnerSize = 24;
    const double titleSize = 14;
    const double countSize = 11;
    const double padding = 12;
    const double borderRadius = 16;

    // تحديد الأيقونة بشكل مبسط
    IconData iconData = _getSimpleIconForCategory(category);
    final Color iconColor = theme.colorScheme.primary;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AzkarListScreen(category: category.name),
            ),
          );
        },
        borderRadius: BorderRadius.circular(borderRadius),
        child: Container(
          padding: const EdgeInsets.all(padding),
          decoration: BoxDecoration(
            color:
                theme.brightness == Brightness.dark
                    ? theme.cardColor
                    : Colors.white,
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(
              color:
                  theme.brightness == Brightness.dark
                      ? Colors.grey.withAlpha(30)
                      : Colors.grey.withAlpha(20),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(5),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة مبسطة ومحسنة للأداء
              Container(
                width: iconSize,
                height: iconSize,
                decoration: BoxDecoration(
                  color: iconColor.withAlpha(20),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  iconData,
                  color: iconColor,
                  size: iconInnerSize,
                ),
              ),
              const SizedBox(height: 8),

              // اسم التصنيف
              Text(
                category.name,
                style: TextStyle(
                  fontSize: titleSize,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 6),

              // عدد الأذكار مبسط
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey.withAlpha(15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${category.count} ذكر',
                  style: TextStyle(
                    fontSize: countSize,
                    fontWeight: FontWeight.w500,
                    color: iconColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // دالة مساعدة مبسطة لتحديد الأيقونة
  IconData _getSimpleIconForCategory(Category category) {
    if (category.name.contains('الصباح')) return Icons.wb_sunny_outlined;
    if (category.name.contains('المساء')) return Icons.nightlight_round;
    if (category.name.contains('النوم')) return Icons.bed;
    if (category.name.contains('الاستيقاظ')) return Icons.alarm;
    if (category.name.contains('الصلاة')) return Icons.mosque;
    if (category.name.contains('الاستغفار')) return Icons.favorite;

    switch (category.icon) {
      case 'sun':
        return Icons.wb_sunny_outlined;
      case 'moon':
        return Icons.nightlight_round;
      case 'bed':
        return Icons.bed;
      case 'alarm':
        return Icons.alarm;
      case 'prayer':
        return Icons.mosque;
      case 'heart':
        return Icons.favorite;
      case 'travel':
        return Icons.directions_car;
      case 'food':
        return Icons.restaurant;
      default:
        return Icons.auto_awesome;
    }
  }

  // بناء قسم الأذكار الخاصة المحسن
  Widget _buildCustomAzkarSection(List<Zikr> customAzkar, ThemeData theme) {
    return Column(
      children: [
        // العنوان
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'أذكاري الخاصة',
                style: TextStyle(
                  fontSize: 18, // تقليل حجم الخط للدقة 1080p
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${customAzkar.length} ذكر',
                style: TextStyle(
                  fontSize: 14,
                  color: theme.colorScheme.onSurface.withAlpha(153),
                ),
              ),
            ],
          ),
        ),
        // الزر
        _buildCustomAzkarButton(theme),
      ],
    );
  }

  // بناء زر الأذكار الخاصة المحسن
  Widget _buildCustomAzkarButton(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              PageTransitions.slideFromRight(const CustomAzkarScreen()),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: EdgeInsets.all(
              MediaQuery.of(context).size.width < 360 ? 14 : 18,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors:
                    theme.brightness == Brightness.dark
                        ? [theme.cardColor, theme.cardColor.withAlpha(240)]
                        : [
                          const Color(0xFFFDFDFD),
                          const Color(0xFFFBFBFB),
                          Colors.white,
                        ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color:
                    theme.brightness == Brightness.dark
                        ? const Color(0x4D9E9E9E)
                        : Colors.grey.withAlpha(20),
                width: 0.8,
              ),
              boxShadow: [
                BoxShadow(
                  color:
                      theme.brightness == Brightness.dark
                          ? Colors.black.withAlpha(40)
                          : Colors.black.withAlpha(12),
                  spreadRadius: 0,
                  blurRadius: theme.brightness == Brightness.dark ? 5 : 12,
                  offset: const Offset(0, 4),
                ),
                if (theme.brightness == Brightness.light)
                  BoxShadow(
                    color: Colors.white.withAlpha(200),
                    spreadRadius: 0,
                    blurRadius: 6,
                    offset: const Offset(0, -2),
                  ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.purple.withAlpha(51),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.bookmark,
                    color: Colors.purple,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'أذكاري الخاصة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'أضف وعدل أذكارك الخاصة',
                        style: TextStyle(
                          fontSize: 12,
                          color: theme.colorScheme.onSurface.withAlpha(153),
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: theme.colorScheme.onSurface.withAlpha(153),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
