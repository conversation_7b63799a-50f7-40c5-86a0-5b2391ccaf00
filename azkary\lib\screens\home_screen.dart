import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';

import '../services/azkar_provider.dart';
import '../services/feedback_provider.dart';
import '../models/azkar_model.dart';
import '../utils/logger.dart';
import 'azkar_list_screen.dart';
import 'custom_azkar_screen.dart';
import 'settings_screen.dart';
// removed unused import
import '../widgets/daily_allah_name.dart';
// removed unused import
import '../widgets/prayer_times_bar.dart';
import '../widgets/islamic_background.dart';
import '../services/prayer_provider.dart';
import '../services/daily_question_service.dart';
import '../utils/page_transitions.dart';
import '../utils/color_extensions.dart';
import '../widgets/animated_question_icon.dart';
import '../models/daily_question.dart';
import 'daily_question_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // التأكد من تحميل البيانات
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final provider = Provider.of<AzkarProvider>(context, listen: false);
      AppLogger.info(
        'Home screen initialized, categories count: ${provider.categories.length}',
      );

      // إذا كانت التصنيفات فارغة، قم بإعادة تحميل البيانات
      if (provider.categories.isEmpty) {
        AppLogger.info('Categories are empty, reloading data...');
        await _reloadData();
      } else {
        AppLogger.info('Categories already loaded:');
        for (var category in provider.categories) {
          AppLogger.info('- ${category.name} (${category.icon})');
        }
      }

      // تهيئة مزود أوقات الصلاة
      if (mounted) {
        final prayerProvider = Provider.of<PrayerProvider>(
          context,
          listen: false,
        );
        prayerProvider.initialize();
      }
    });
  }

  // دالة لإعادة تحميل البيانات
  Future<void> _reloadData() async {
    try {
      AppLogger.info('Reloading data from home screen');
      final provider = Provider.of<AzkarProvider>(context, listen: false);

      // إعادة تهيئة قاعدة البيانات وتحميل البيانات
      await provider.reloadData();

      AppLogger.info(
        'Data reloaded, categories count: ${provider.categories.length}',
      );
      if (provider.categories.isNotEmpty) {
        AppLogger.info('Categories loaded successfully:');
        for (var category in provider.categories) {
          AppLogger.info('- ${category.name} (${category.icon})');
        }
      } else {
        AppLogger.warning(
          'No categories loaded after reload, forcing another reload',
        );
        // محاولة إعادة التحميل مرة أخرى
        await provider.reloadData();
      }

      // تحديث واجهة المستخدم
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      AppLogger.error('Error reloading data from home screen: $e');
      // محاولة إعادة التحميل في حالة الخطأ
      if (mounted) {
        final provider = Provider.of<AzkarProvider>(context, listen: false);
        // محاولة إعادة التحميل مرة أخرى
        await provider.reloadData();
      }

      if (mounted) {
        setState(() {});
      }
    }
  }

  // دالة لتنسيق التاريخ بالعربية
  String _getFormattedDate() {
    final now = DateTime.now();
    final formatter = DateFormat('EEEE، d MMMM yyyy', 'ar');
    return formatter.format(now);
  }

  // بناء بطاقة السؤال اليومي
  Widget _buildDailyQuestionCard() {
    final theme = Theme.of(context);

    return Selector<
      DailyQuestionService,
      ({bool hasCompleted, int answered, int total, QuestionStats stats})
    >(
      selector:
          (context, service) => (
            hasCompleted: service.hasCompletedAllToday,
            answered: service.answeredQuestionsToday,
            total: service.totalDailyQuestions,
            stats: service.stats,
          ),
      builder: (context, data, child) {
        // إذا تم الإجابة على جميع الأسئلة، لا نعرض البطاقة (ستظهر الأيقونة في شريط التطبيق فقط)
        if (data.hasCompleted) {
          return const SizedBox.shrink();
        }

        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: Container(
            key: ValueKey(data.hasCompleted),
            margin: const EdgeInsets.all(16),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  Navigator.of(context).push(
                    PageTransitions.slideFromRight(const DailyQuestionScreen()),
                  );
                },
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.primaryWithOpacity(
                          theme.colorScheme.primary,
                          0.15,
                        ),
                        AppColors.primaryWithOpacity(
                          theme.colorScheme.primary,
                          0.08,
                        ),
                        AppColors.primaryWithOpacity(
                          theme.colorScheme.primary,
                          0.05,
                        ),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: AppColors.primaryWithOpacity(
                        theme.colorScheme.primary,
                        0.3,
                      ),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color:
                            theme.brightness == Brightness.dark
                                ? AppColors.blackWithOpacity(0.4)
                                : AppColors.primaryWithOpacity(
                                  theme.colorScheme.primary,
                                  0.2,
                                ),
                        blurRadius: 15,
                        spreadRadius: 2,
                        offset: const Offset(0, 5),
                      ),
                      BoxShadow(
                        color: AppColors.primaryWithOpacity(
                          theme.colorScheme.primary,
                          0.1,
                        ),
                        blurRadius: 30,
                        spreadRadius: 5,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      // أيقونة السؤال المتحركة
                      Stack(
                        children: [
                          AnimatedQuestionIcon(
                            isAnswered: data.hasCompleted,
                            size: 70,
                            onTap: () {
                              Navigator.of(context).push(
                                PageTransitions.slideFromRight(
                                  const DailyQuestionScreen(),
                                ),
                              );
                            },
                          ),
                          if (data.answered > 0)
                            Positioned(
                              top: 5,
                              right: 5,
                              child: Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: Colors.orange,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 2,
                                  ),
                                ),
                                child: Text(
                                  '${data.answered}/${data.total}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),

                      const SizedBox(width: 20),

                      // محتوى البطاقة
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  'الأسئلة اليومية',
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.orangeWithOpacity(0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '${data.answered}/${data.total}',
                                    style: const TextStyle(
                                      fontSize: 10,
                                      color: Colors.orange,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'اختبر معلوماتك الإسلامية مع 3 أسئلة جديدة كل يوم 🧠',
                              style: TextStyle(
                                fontSize: 15,
                                color: AppColors.primaryWithOpacity(
                                  theme.colorScheme.onSurface,
                                  0.8,
                                ),
                                height: 1.4,
                              ),
                            ),
                            const SizedBox(height: 12),
                            if (data.stats.totalQuestions > 0)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.primaryWithOpacity(
                                    theme.colorScheme.primary,
                                    0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                  border: Border.all(
                                    color: AppColors.primaryWithOpacity(
                                      theme.colorScheme.primary,
                                      0.2,
                                    ),
                                  ),
                                ),
                                child: Text(
                                  'دقتك: ${data.stats.accuracy.toStringAsFixed(1)}% • السلسلة: ${data.stats.currentStreak} 🏆',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: theme.colorScheme.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),

                      // سهم الانتقال مع تأثير
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primaryWithOpacity(
                            theme.colorScheme.primary,
                            0.1,
                          ),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_forward_ios,
                          color: theme.colorScheme.primary,
                          size: 18,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('أذكاري'),
        actions: [
          // أيقونة السؤال اليومي المتحركة
          Selector<
            DailyQuestionService,
            ({bool hasCompleted, int answered, int total})
          >(
            selector:
                (context, service) => (
                  hasCompleted: service.hasCompletedAllToday,
                  answered: service.answeredQuestionsToday,
                  total: service.totalDailyQuestions,
                ),
            builder: (context, data, child) {
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: Stack(
                  children: [
                    AnimatedQuestionIcon(
                      isAnswered: data.hasCompleted,
                      showMiniVersion: true,
                      onTap: () {
                        Navigator.of(context).push(
                          PageTransitions.slideFromRight(
                            const DailyQuestionScreen(),
                          ),
                        );
                      },
                    ),
                    if (!data.hasCompleted && data.answered > 0)
                      Positioned(
                        top: -2,
                        right: -2,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.orange,
                            shape: BoxShape.circle,
                          ),
                          child: Text(
                            '${data.answered}/${data.total}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 8,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                PageTransitions.slideFromRight(const SettingsScreen()),
              );
            },
          ),
        ],
      ),
      body: IslamicBackground(
        opacity: theme.brightness == Brightness.dark ? 0.02 : 0.03,
        showPattern: true,
        showGradient: false,
        child: Consumer<AzkarProvider>(
          builder: (context, provider, child) {
            if (provider.isLoading) {
              return const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                ),
              );
            }

            // التحقق من وجود البيانات
            if (provider.categories.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'لا توجد بيانات متاحة',
                      style: TextStyle(fontSize: 18),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _reloadData,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('إعادة تحميل البيانات'),
                    ),
                  ],
                ),
              );
            }

            return CustomScrollView(
              // تحسينات الأداء
              cacheExtent: 1000, // تخزين مؤقت للعناصر
              physics: const BouncingScrollPhysics(), // تمرير سلس
              slivers: [
                // شريط أوقات الصلاة
                SliverToBoxAdapter(
                  child: RepaintBoundary(child: const PrayerTimesBar()),
                ),

                // بطاقة السؤال اليومي
                SliverToBoxAdapter(
                  child: RepaintBoundary(child: _buildDailyQuestionCard()),
                ),

                // قسم ذكر اليوم
                if (provider.dailyZikr != null)
                  SliverToBoxAdapter(
                    child: RepaintBoundary(
                      child: _buildDailyZikrSection(provider.dailyZikr!),
                    ),
                  ),

                // قسم تصنيفات الأذكار
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'تصنيفات الأذكار',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                ),

                // شبكة التصنيفات المحسنة
                SliverToBoxAdapter(
                  child: RepaintBoundary(
                    child: _buildOptimizedCategoriesGrid(provider.categories),
                  ),
                ),

                // قسم الأذكار الخاصة
                SliverToBoxAdapter(
                  child: _buildCustomAzkarSection(provider, theme),
                ),

                // قسم اسم الله اليومي
                SliverToBoxAdapter(
                  child: const Padding(
                    padding: EdgeInsets.fromLTRB(16, 24, 16, 8),
                    child: Text(
                      'اسم الله اليومي',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

                // مكون اسم الله اليومي
                SliverToBoxAdapter(
                  child: RepaintBoundary(child: const DailyAllahName()),
                ),

                // مساحة إضافية في النهاية
                const SliverToBoxAdapter(child: SizedBox(height: 20)),
              ],
            );
          },
        ),
      ),
    );
  }

  // بناء قسم ذكر اليوم المحسن والمتجاوب
  Widget _buildDailyZikrSection(Zikr dailyZikr) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    // تحديد الأحجام والمسافات حسب حجم الشاشة
    final double margin = isSmallScreen ? 12 : 16;
    final double padding = isSmallScreen ? 14 : 16;
    final double borderRadius = isSmallScreen ? 16 : 20;
    final double titleSize = isSmallScreen ? 16 : 18;
    final double textSize = isSmallScreen ? 15 : 16;
    final double sourceSize = isSmallScreen ? 11 : 12;
    final double dateSize = isSmallScreen ? 10 : 12;

    return Hero(
      tag: 'daily-zikr',
      child: Container(
        margin: EdgeInsets.all(margin),
        padding: EdgeInsets.all(padding),
        width: double.infinity,
        decoration: BoxDecoration(
          color:
              theme.brightness == Brightness.dark
                  ? theme.cardColor
                  : Colors.white,
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(
            color:
                theme.brightness == Brightness.dark
                    ? Colors.grey.withAlpha(30)
                    : Colors.grey.withAlpha(20),
            width: 1.0,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(8),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // العنوان والتاريخ المحسن
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 10 : 12,
                      vertical: isSmallScreen ? 6 : 8,
                    ),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          theme.colorScheme.primary.withAlpha(30),
                          theme.colorScheme.primary.withAlpha(15),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(
                        isSmallScreen ? 16 : 20,
                      ),
                      border: Border.all(
                        color: theme.colorScheme.primary.withAlpha(40),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.auto_awesome,
                          color:
                              theme.brightness == Brightness.dark
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                          size: isSmallScreen ? 16 : 18,
                        ),
                        SizedBox(width: isSmallScreen ? 6 : 8),
                        Flexible(
                          child: Text(
                            'ذكري اليومي',
                            style: TextStyle(
                              fontSize: titleSize,
                              fontWeight: FontWeight.bold,
                              color:
                                  theme.brightness == Brightness.dark
                                      ? Colors.grey.shade300
                                      : Colors.grey.shade700,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 8 : 12),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.calendar_today,
                      color:
                          theme.brightness == Brightness.dark
                              ? Colors.grey.shade400
                              : Colors.grey.shade600,
                      size: isSmallScreen ? 10 : 12,
                    ),
                    SizedBox(width: isSmallScreen ? 3 : 4),
                    Text(
                      _getFormattedDate(),
                      style: TextStyle(
                        fontSize: dateSize,
                        fontWeight: FontWeight.bold,
                        color:
                            theme.brightness == Brightness.dark
                                ? Colors.grey.shade400
                                : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 14 : 16),

            // نص الذكر مع تحسينات
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface.withAlpha(50),
                borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                border: Border.all(
                  color: theme.colorScheme.outline.withAlpha(20),
                  width: 1,
                ),
              ),
              child: Text(
                dailyZikr.text,
                style: TextStyle(
                  fontSize: textSize,
                  height: 1.6,
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                softWrap: true,
                overflow: TextOverflow.visible,
              ),
            ),
            SizedBox(height: isSmallScreen ? 10 : 12),

            // المصدر والأزرار
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 8 : 10,
                      vertical: isSmallScreen ? 4 : 6,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.menu_book,
                          color:
                              theme.brightness == Brightness.dark
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                          size: isSmallScreen ? 12 : 14,
                        ),
                        SizedBox(width: isSmallScreen ? 4 : 6),
                        Flexible(
                          child: Text(
                            'رواه ${dailyZikr.source}',
                            style: TextStyle(
                              fontSize: sourceSize,
                              fontWeight: FontWeight.bold,
                              color:
                                  theme.brightness == Brightness.dark
                                      ? Colors.grey.shade400
                                      : Colors.grey.shade600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 8 : 12),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildModernActionButton(
                      Icons.copy_outlined,
                      'نسخ',
                      theme.colorScheme.primary,
                      isSmallScreen,
                      onPressed: () => _copyToClipboard(dailyZikr.text),
                    ),
                    SizedBox(width: isSmallScreen ? 8 : 12),
                    _buildModernActionButton(
                      Icons.share_outlined,
                      'مشاركة',
                      theme.colorScheme.primary,
                      isSmallScreen,
                      onPressed: () => _shareZikr(dailyZikr),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء زر عمل حديث ومحسن
  Widget _buildModernActionButton(
    IconData icon,
    String tooltip,
    Color color,
    bool isSmallScreen, {
    VoidCallback? onPressed,
  }) {
    final size = isSmallScreen ? 32.0 : 36.0;
    final iconSize = isSmallScreen ? 16.0 : 18.0;

    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(size / 2),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [color.withAlpha(30), color.withAlpha(15)],
          ),
          shape: BoxShape.circle,
          border: Border.all(color: color.withAlpha(60), width: 1),
          boxShadow: [
            BoxShadow(
              color: color.withAlpha(20),
              blurRadius: 4,
              spreadRadius: 1,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Tooltip(
          message: tooltip,
          child: Icon(icon, size: iconSize, color: color),
        ),
      ),
    );
  }

  // نسخ النص إلى الحافظة
  void _copyToClipboard(String text) {
    // تنفيذ نسخ النص إلى الحافظة
    Clipboard.setData(ClipboardData(text: text));

    // تنفيذ اهتزاز خفيف
    final feedbackProvider = Provider.of<FeedbackProvider>(
      context,
      listen: false,
    );
    feedbackProvider.lightHapticFeedback();

    // عرض رسالة تأكيد
    feedbackProvider.showSuccessSnackBar(
      context,
      'تم نسخ النص',
      icon: Icons.check_circle,
    );
  }

  // مشاركة الذكر
  void _shareZikr(Zikr zikr) {
    // تنفيذ مشاركة الذكر
    final text = '${zikr.text}\n\nرواه ${zikr.source}\n\nمن تطبيق أذكاري';

    // استخدام SharePlus بالطريقة الصحيحة
    final box = context.findRenderObject() as RenderBox?;
    SharePlus.instance.share(
      ShareParams(
        text: text,
        sharePositionOrigin:
            box != null ? box.localToGlobal(Offset.zero) & box.size : null,
      ),
    );

    // تنفيذ اهتزاز خفيف
    final feedbackProvider = Provider.of<FeedbackProvider>(
      context,
      listen: false,
    );
    feedbackProvider.lightHapticFeedback();

    // عرض رسالة تأكيد
    feedbackProvider.showInfoSnackBar(context, 'تمت مشاركة الذكر');
  }

  // بناء شبكة التصنيفات
  Widget _buildCategoriesGrid(List<Category> categories) {
    // الحصول على حجم الشاشة
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isMediumScreen = screenSize.width >= 360 && screenSize.width < 600;

    // تحديد عدد الأعمدة بناءً على حجم الشاشة
    // تعديل: جعل عدد الأعمدة 2 حتى في الشاشات الصغيرة
    int crossAxisCount = isSmallScreen ? 2 : (isMediumScreen ? 2 : 3);

    // تحديد نسبة العرض إلى الارتفاع
    double childAspectRatio =
        isSmallScreen ? 1.0 : (isMediumScreen ? 1.0 : 1.1);

    // إنشاء قائمة جديدة تحتوي على التصنيفات الحالية
    final List<Widget> gridItems = [];

    // إضافة عناصر التصنيفات الحالية
    for (var category in categories) {
      gridItems.add(_buildCategoryItem(category));
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: isSmallScreen ? 8 : 16,
        mainAxisSpacing: isSmallScreen ? 8 : 16,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: gridItems.length,
      itemBuilder: (context, index) {
        return gridItems[index];
      },
    );
  }

  // بناء عنصر التصنيف المحسن والمتجاوب
  Widget _buildCategoryItem(Category category) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isMediumScreen = screenSize.width < 600;

    // تحديد الأحجام حسب حجم الشاشة
    final double iconSize = isSmallScreen ? 44 : (isMediumScreen ? 52 : 56);
    final double iconInnerSize =
        isSmallScreen ? 22 : (isMediumScreen ? 26 : 28);
    final double titleSize = isSmallScreen ? 13 : (isMediumScreen ? 15 : 16);
    final double countSize = isSmallScreen ? 9 : (isMediumScreen ? 11 : 12);
    final double padding = isSmallScreen ? 10 : (isMediumScreen ? 14 : 16);
    final double borderRadius = isSmallScreen ? 16 : (isMediumScreen ? 18 : 20);

    // تحديد الأيقونة مع ألوان سادة
    IconData iconData;
    final Color iconBgColor = theme.colorScheme.primary.withAlpha(20);
    final Color iconColor = theme.colorScheme.primary;

    if (category.name.contains('الصباح')) {
      iconData = Icons.wb_sunny_outlined;
    } else if (category.name.contains('المساء')) {
      iconData = Icons.nightlight_round;
    } else if (category.name.contains('النوم')) {
      iconData = Icons.bed;
    } else if (category.name.contains('الاستيقاظ')) {
      iconData = Icons.alarm;
    } else if (category.name.contains('الصلاة')) {
      iconData = Icons.mosque;
    } else if (category.name.contains('الاستغفار')) {
      iconData = Icons.favorite;
    } else {
      // الحالة الافتراضية
      switch (category.icon) {
        case 'sun':
          iconData = Icons.wb_sunny_outlined;
          break;
        case 'moon':
          iconData = Icons.nightlight_round;
          break;
        case 'bed':
          iconData = Icons.bed;
          break;
        case 'alarm':
          iconData = Icons.alarm;
          break;
        case 'prayer':
          iconData = Icons.mosque;
          break;
        case 'heart':
          iconData = Icons.favorite;
          break;
        case 'travel':
          iconData = Icons.directions_car;
          break;
        case 'food':
          iconData = Icons.restaurant;
          break;
        default:
          iconData = Icons.auto_awesome;
      }
    }

    return AnimatedWidgets.fadeInCard(
      index: category.id,
      child: Hero(
        tag: 'category-${category.id}',
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                PageTransitions.islamicTransition(
                  AzkarListScreen(category: category.name),
                ),
              );
            },
            borderRadius: BorderRadius.circular(borderRadius),
            child: Container(
              padding: EdgeInsets.all(padding),
              decoration: BoxDecoration(
                color:
                    theme.brightness == Brightness.dark
                        ? theme.cardColor
                        : Colors.white,
                borderRadius: BorderRadius.circular(borderRadius),
                border: Border.all(
                  color:
                      theme.brightness == Brightness.dark
                          ? Colors.grey.withAlpha(30)
                          : Colors.grey.withAlpha(20),
                  width: 0.8,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(8),
                    blurRadius: 8,
                    spreadRadius: 0,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // أيقونة محسنة مع تدرج ناعم
                  Container(
                    width: iconSize,
                    height: iconSize,
                    decoration: BoxDecoration(
                      color: iconBgColor,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: theme.colorScheme.primary.withAlpha(40),
                        width: 1.0,
                      ),
                    ),
                    child: Icon(
                      iconData,
                      color: iconColor, // لون أيقونة رمادي
                      size: iconInnerSize,
                    ),
                  ),
                  SizedBox(
                    height: isSmallScreen ? 10 : (isMediumScreen ? 12 : 14),
                  ),

                  // اسم التصنيف
                  Text(
                    category.name,
                    style: TextStyle(
                      fontSize: titleSize,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(
                    height: isSmallScreen ? 6 : (isMediumScreen ? 8 : 10),
                  ),

                  // عدد الأذكار مع تصميم محسن وناعم
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal:
                          isSmallScreen ? 8 : (isMediumScreen ? 10 : 12),
                      vertical: isSmallScreen ? 4 : (isMediumScreen ? 5 : 6),
                    ),
                    decoration: BoxDecoration(
                      color:
                          theme.brightness == Brightness.dark
                              ? Colors.grey.withAlpha(20)
                              : Colors.grey.withAlpha(15),
                      borderRadius: BorderRadius.circular(
                        isSmallScreen ? 12 : 14,
                      ),
                      border: Border.all(
                        color:
                            theme.brightness == Brightness.dark
                                ? Colors.grey.withAlpha(40)
                                : Colors.grey.withAlpha(25),
                        width: 0.8,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.format_list_bulleted,
                          color:
                              theme.brightness == Brightness.dark
                                  ? iconColor
                                  : iconColor, // لون رمادي موحد
                          size: isSmallScreen ? 10 : 12,
                        ),
                        SizedBox(width: isSmallScreen ? 3 : 4),
                        Text(
                          '${category.count} ذكر',
                          style: TextStyle(
                            fontSize: countSize,
                            fontWeight: FontWeight.bold,
                            color:
                                theme.brightness == Brightness.dark
                                    ? iconColor
                                    : iconColor, // لون رمادي موحد
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // بناء شبكة التصنيفات المحسنة
  Widget _buildOptimizedCategoriesGrid(List<Category> categories) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isMediumScreen = screenSize.width < 600;

    int crossAxisCount = isSmallScreen ? 2 : (isMediumScreen ? 2 : 3);
    double childAspectRatio =
        isSmallScreen ? 1.0 : (isMediumScreen ? 1.0 : 1.1);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 16),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        // تحسينات الأداء
        addAutomaticKeepAlives: false,
        addRepaintBoundaries: true,
        addSemanticIndexes: false,
        cacheExtent: 500,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: isSmallScreen ? 8 : 16,
          mainAxisSpacing: isSmallScreen ? 8 : 16,
          childAspectRatio: childAspectRatio,
        ),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          return RepaintBoundary(
            child: _buildOptimizedCategoryItem(categories[index]),
          );
        },
      ),
    );
  }

  // بناء عنصر التصنيف المحسن
  Widget _buildOptimizedCategoryItem(Category category) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isMediumScreen = screenSize.width < 600;

    final double iconSize = isSmallScreen ? 44 : (isMediumScreen ? 52 : 56);
    final double iconInnerSize =
        isSmallScreen ? 22 : (isMediumScreen ? 26 : 28);
    final double titleSize = isSmallScreen ? 13 : (isMediumScreen ? 15 : 16);
    final double countSize = isSmallScreen ? 9 : (isMediumScreen ? 11 : 12);
    final double padding = isSmallScreen ? 10 : (isMediumScreen ? 14 : 16);
    final double borderRadius = isSmallScreen ? 16 : (isMediumScreen ? 18 : 20);

    // تحديد الأيقونة
    IconData iconData = _getIconForCategory(category);
    final Color iconBgColor = theme.colorScheme.primary.withAlpha(20);
    final Color iconColor = theme.colorScheme.primary;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            PageTransitions.islamicTransition(
              AzkarListScreen(category: category.name),
            ),
          );
        },
        borderRadius: BorderRadius.circular(borderRadius),
        child: Container(
          padding: EdgeInsets.all(padding),
          decoration: BoxDecoration(
            color:
                theme.brightness == Brightness.dark
                    ? theme.cardColor
                    : Colors.white,
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(
              color:
                  theme.brightness == Brightness.dark
                      ? Colors.grey.withAlpha(30)
                      : Colors.grey.withAlpha(20),
              width: 0.8,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(8),
                blurRadius: 8,
                spreadRadius: 0,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة محسنة
              Container(
                width: iconSize,
                height: iconSize,
                decoration: BoxDecoration(
                  color: iconBgColor,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: theme.colorScheme.primary.withAlpha(40),
                    width: 1.0,
                  ),
                ),
                child: Icon(iconData, color: iconColor, size: iconInnerSize),
              ),
              SizedBox(height: isSmallScreen ? 10 : (isMediumScreen ? 12 : 14)),

              // اسم التصنيف
              Text(
                category.name,
                style: TextStyle(
                  fontSize: titleSize,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: isSmallScreen ? 6 : (isMediumScreen ? 8 : 10)),

              // عدد الأذكار
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 8 : (isMediumScreen ? 10 : 12),
                  vertical: isSmallScreen ? 4 : (isMediumScreen ? 5 : 6),
                ),
                decoration: BoxDecoration(
                  color:
                      theme.brightness == Brightness.dark
                          ? Colors.grey.withAlpha(20)
                          : Colors.grey.withAlpha(15),
                  borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 14),
                  border: Border.all(
                    color:
                        theme.brightness == Brightness.dark
                            ? Colors.grey.withAlpha(40)
                            : Colors.grey.withAlpha(25),
                    width: 0.8,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.format_list_bulleted,
                      color: iconColor,
                      size: isSmallScreen ? 10 : 12,
                    ),
                    SizedBox(width: isSmallScreen ? 3 : 4),
                    Text(
                      '${category.count} ذكر',
                      style: TextStyle(
                        fontSize: countSize,
                        fontWeight: FontWeight.bold,
                        color: iconColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // دالة مساعدة لتحديد الأيقونة
  IconData _getIconForCategory(Category category) {
    if (category.name.contains('الصباح')) {
      return Icons.wb_sunny_outlined;
    } else if (category.name.contains('المساء')) {
      return Icons.nightlight_round;
    } else if (category.name.contains('النوم')) {
      return Icons.bed;
    } else if (category.name.contains('الاستيقاظ')) {
      return Icons.alarm;
    } else if (category.name.contains('الصلاة')) {
      return Icons.mosque;
    } else if (category.name.contains('الاستغفار')) {
      return Icons.favorite;
    } else {
      switch (category.icon) {
        case 'sun':
          return Icons.wb_sunny_outlined;
        case 'moon':
          return Icons.nightlight_round;
        case 'bed':
          return Icons.bed;
        case 'alarm':
          return Icons.alarm;
        case 'prayer':
          return Icons.mosque;
        case 'heart':
          return Icons.favorite;
        case 'travel':
          return Icons.directions_car;
        case 'food':
          return Icons.restaurant;
        default:
          return Icons.auto_awesome;
      }
    }
  }

  // بناء قسم الأذكار الخاصة المحسن
  Widget _buildCustomAzkarSection(AzkarProvider provider, ThemeData theme) {
    return Column(
      children: [
        // العنوان
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'أذكاري الخاصة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              Text(
                '${provider.customAzkar.length} ذكر',
                style: TextStyle(
                  fontSize: 14,
                  color: theme.colorScheme.onSurface.withAlpha(153),
                ),
              ),
            ],
          ),
        ),
        // الزر
        _buildCustomAzkarButton(theme),
      ],
    );
  }

  // بناء زر الأذكار الخاصة المحسن
  Widget _buildCustomAzkarButton(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              PageTransitions.slideFromRight(const CustomAzkarScreen()),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: EdgeInsets.all(
              MediaQuery.of(context).size.width < 360 ? 14 : 18,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors:
                    theme.brightness == Brightness.dark
                        ? [theme.cardColor, theme.cardColor.withAlpha(240)]
                        : [
                          const Color(0xFFFDFDFD),
                          const Color(0xFFFBFBFB),
                          Colors.white,
                        ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color:
                    theme.brightness == Brightness.dark
                        ? const Color(0x4D9E9E9E)
                        : Colors.grey.withAlpha(20),
                width: 0.8,
              ),
              boxShadow: [
                BoxShadow(
                  color:
                      theme.brightness == Brightness.dark
                          ? Colors.black.withAlpha(40)
                          : Colors.black.withAlpha(12),
                  spreadRadius: 0,
                  blurRadius: theme.brightness == Brightness.dark ? 5 : 12,
                  offset: const Offset(0, 4),
                ),
                if (theme.brightness == Brightness.light)
                  BoxShadow(
                    color: Colors.white.withAlpha(200),
                    spreadRadius: 0,
                    blurRadius: 6,
                    offset: const Offset(0, -2),
                  ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.purple.withAlpha(51),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.bookmark,
                    color: Colors.purple,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'أذكاري الخاصة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'أضف وعدل أذكارك الخاصة',
                        style: TextStyle(
                          fontSize: 12,
                          color: theme.colorScheme.onSurface.withAlpha(153),
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: theme.colorScheme.onSurface.withAlpha(153),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
