import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/allah_names_data.dart';
import '../services/daily_content_service.dart';
import '../screens/allah_names_screen.dart';
import '../widgets/page_transitions.dart';
import 'islamic_pattern.dart';

/// مكون لعرض اسم من أسماء الله الحسنى يتغير يومياً
class DailyAllahName extends StatefulWidget {
  const DailyAllahName({super.key});

  @override
  State<DailyAllahName> createState() => _DailyAllahNameState();
}

class _DailyAllahNameState extends State<DailyAllahName>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  AllahName? _dailyName;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();

    // إعداد متحكم الحركة المبسط
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300), // تقليل المدة
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut, // منحنى بسيط
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.98, end: 1.0).animate(
      // تقليل التأثير
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut, // منحنى بسيط
      ),
    );

    _loadDailyName();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// تحميل اسم الله اليومي
  Future<void> _loadDailyName() async {
    try {
      final dailyNameIndex = DailyContentService.getDailyAllahNameIndex();
      setState(() {
        _dailyName = allahNamesData[dailyNameIndex];
        _isLoading = false;
      });
      _animationController.forward();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    if (_isLoading) {
      return const SizedBox(
        height: 150,
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (_dailyName == null) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(scale: _scaleAnimation, child: child),
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color:
              isDarkMode
                  ? theme.colorScheme.surface
                  : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(
                red: 0,
                green: 0,
                blue: 0,
                alpha: 0.05,
              ),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: theme.colorScheme.primary.withValues(
              red: theme.colorScheme.primary.r.toDouble(),
              green: theme.colorScheme.primary.g.toDouble(),
              blue: theme.colorScheme.primary.b.toDouble(),
              alpha: 0.2,
            ),
            width: 1,
          ),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () {
              navigateTo(
                context,
                const AllahNamesScreen(),
                type: PageTransitionType.rightToLeftWithFade,
                duration: const Duration(milliseconds: 400),
              );
            },
            child: Stack(
              children: [
                // خلفية إسلامية
                Positioned.fill(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Opacity(
                      opacity: 0.03,
                      child: IslamicPattern(
                        color: theme.colorScheme.primary,
                        opacity: 0.05,
                      ),
                    ),
                  ),
                ),

                // المحتوى
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // عنوان
                      Row(
                        children: [
                          Icon(
                            Icons.auto_awesome,
                            size: 18,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'اسم الله اليومي',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                          const Spacer(),
                          // زر النسخ
                          IconButton(
                            icon: const Icon(Icons.copy, size: 18),
                            onPressed: () {
                              final text =
                                  '${_dailyName!.name}: ${_dailyName!.meaning}\n${_dailyName!.description}';
                              Clipboard.setData(ClipboardData(text: text));

                              // إظهار رسالة تأكيد
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('تم نسخ اسم الله ومعناه'),
                                  duration: Duration(seconds: 2),
                                ),
                              );
                            },
                            tooltip: 'نسخ',
                            color: theme.colorScheme.primary,
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // اسم الله
                      Center(
                        child: Text(
                          _dailyName!.name,
                          style: TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // معنى الاسم
                      Center(
                        child: Text(
                          _dailyName!.meaning,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 12),

                      // شرح الاسم
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withValues(
                            red: theme.colorScheme.primary.r.toDouble(),
                            green: theme.colorScheme.primary.g.toDouble(),
                            blue: theme.colorScheme.primary.b.toDouble(),
                            alpha: 0.05,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _dailyName!.description,
                          style: TextStyle(
                            fontSize: 14,
                            height: 1.5,
                            color: theme.colorScheme.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // نص توضيحي
                      Text(
                        'انقر للاطلاع على جميع أسماء الله الحسنى',
                        style: TextStyle(
                          fontSize: 12,
                          color: theme.colorScheme.onSurface.withValues(
                            red: theme.colorScheme.onSurface.r.toDouble(),
                            green: theme.colorScheme.onSurface.g.toDouble(),
                            blue: theme.colorScheme.onSurface.b.toDouble(),
                            alpha: 0.6,
                          ),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
