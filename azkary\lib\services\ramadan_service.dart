import '../models/ramadan_model.dart';

/// خدمة معلومات وبيانات شهر رمضان
class RamadanService {
  static final RamadanService _instance = RamadanService._internal();
  factory RamadanService() => _instance;
  RamadanService._internal();

  /// الحصول على معلومات رمضان للسنة الحالية
  RamadanInfo getCurrentRamadanInfo() {
    final now = DateTime.now();
    final currentYear = now.year;

    // تواريخ رمضان التقريبية (يجب تحديثها سنوياً)
    final ramadanDates = _getRamadanDates(currentYear);

    final startDate = ramadanDates['start']!;
    final endDate = ramadanDates['end']!;

    final isCurrentlyRamadan = now.isAfter(startDate) && now.isBefore(endDate);

    int daysRemaining;
    Duration timeUntilRamadan;

    if (isCurrentlyRamadan) {
      daysRemaining = endDate.difference(now).inDays;
      timeUntilRamadan = Duration.zero;
    } else if (now.isBefore(startDate)) {
      daysRemaining = 30; // إجمالي أيام رمضان
      timeUntilRamadan = startDate.difference(now);
    } else {
      // رمضان انتهى، احسب للسنة القادمة
      final nextYearDates = _getRamadanDates(currentYear + 1);
      daysRemaining = 30;
      timeUntilRamadan = nextYearDates['start']!.difference(now);
    }

    return RamadanInfo(
      startDate: startDate,
      endDate: endDate,
      year: currentYear,
      totalDays: 30,
      isCurrentlyRamadan: isCurrentlyRamadan,
      daysRemaining: daysRemaining,
      timeUntilRamadan: timeUntilRamadan,
    );
  }

  /// الحصول على تواريخ رمضان لسنة معينة
  Map<String, DateTime> _getRamadanDates(int year) {
    // تواريخ تقريبية - يجب تحديثها بناءً على الحسابات الفلكية الدقيقة
    switch (year) {
      case 2024:
        return {'start': DateTime(2024, 3, 11), 'end': DateTime(2024, 4, 9)};
      case 2025:
        return {'start': DateTime(2025, 2, 28), 'end': DateTime(2025, 3, 29)};
      case 2026:
        return {'start': DateTime(2026, 2, 17), 'end': DateTime(2026, 3, 18)};
      default:
        // تقدير تقريبي بناءً على دورة القمر
        const baseYear = 2024;
        final yearDiff = year - baseYear;
        final daysDiff = (yearDiff * 354.37).round(); // السنة القمرية

        return {
          'start': DateTime(2024, 3, 11).add(Duration(days: daysDiff)),
          'end': DateTime(2024, 4, 9).add(Duration(days: daysDiff)),
        };
    }
  }

  /// الحصول على الأدعية الرمضانية
  List<RamadanDua> getRamadanDuas() {
    return [
      // دعاء الإفطار
      const RamadanDua(
        id: 'iftar_1',
        title: 'دعاء الإفطار',
        arabicText: 'اللَّهُمَّ لَكَ صُمْتُ وَعَلَى رِزْقِكَ أَفْطَرْتُ',
        transliteration: 'Allahumma laka sumtu wa ala rizqika aftartu',
        translation: 'اللهم لك صمت وعلى رزقك أفطرت',
        category: 'iftar',
        source: 'أبو داود',
        benefits: 'دعاء مستجاب عند الإفطار',
        icon: RamadanIcons.prayer,
        color: RamadanColors.gold,
      ),

      const RamadanDua(
        id: 'iftar_2',
        title: 'دعاء آخر للإفطار',
        arabicText:
            'ذَهَبَ الظَّمَأُ وَابْتَلَّتِ الْعُرُوقُ وَثَبَتَ الْأَجْرُ إِنْ شَاءَ اللَّهُ',
        transliteration:
            'Dhahaba al-zama wa abtalat al-uruq wa thabata al-ajru in sha Allah',
        translation: 'ذهب الظمأ وابتلت العروق وثبت الأجر إن شاء الله',
        category: 'iftar',
        source: 'أبو داود',
        icon: RamadanIcons.prayer,
        color: RamadanColors.darkGreen,
      ),

      // دعاء السحور
      const RamadanDua(
        id: 'suhoor_1',
        title: 'دعاء السحور',
        arabicText:
            'اللَّهُمَّ بَارِكْ لَنَا فِيمَا رَزَقْتَنَا وَقِنَا عَذَابَ النَّارِ',
        transliteration:
            'Allahumma barik lana fima razaqtana wa qina adhab an-nar',
        translation: 'اللهم بارك لنا فيما رزقتنا وقنا عذاب النار',
        category: 'suhoor',
        benefits: 'بركة في الرزق والوقاية من النار',
        icon: RamadanIcons.crescent,
        color: RamadanColors.emerald,
      ),

      // أدعية ليلة القدر
      const RamadanDua(
        id: 'laylat_1',
        title: 'دعاء ليلة القدر',
        arabicText:
            'اللَّهُمَّ إِنَّكَ عَفُوٌّ تُحِبُّ الْعَفْوَ فَاعْفُ عَنِّي',
        transliteration: 'Allahumma innaka afuwwun tuhibbu al-afwa fa\'fu anni',
        translation: 'اللهم إنك عفو تحب العفو فاعف عني',
        category: 'laylatAlQadr',
        source: 'الترمذي',
        benefits: 'دعاء مستجاب في ليلة القدر',
        icon: RamadanIcons.star,
        color: RamadanColors.gold,
      ),

      // أدعية العشر الأواخر
      const RamadanDua(
        id: 'last_ten_1',
        title: 'دعاء العشر الأواخر',
        arabicText: 'اللَّهُمَّ أَعْتِقْ رَقَبَتِي مِنَ النَّارِ',
        transliteration: 'Allahumma a\'tiq raqabati min an-nar',
        translation: 'اللهم أعتق رقبتي من النار',
        category: 'lastTenDays',
        benefits: 'العتق من النار',
        icon: RamadanIcons.peace,
        color: RamadanColors.darkGreen,
      ),

      // الأدعية المستجابة
      const RamadanDua(
        id: 'accepted_1',
        title: 'دعاء مستجاب',
        arabicText:
            'رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً وَقِنَا عَذَابَ النَّارِ',
        transliteration:
            'Rabbana atina fi ad-dunya hasanatan wa fi al-akhirati hasanatan wa qina adhab an-nar',
        translation: 'ربنا آتنا في الدنيا حسنة وفي الآخرة حسنة وقنا عذاب النار',
        category: 'accepted',
        source: 'القرآن الكريم',
        benefits: 'دعاء جامع لخير الدنيا والآخرة',
        icon: RamadanIcons.favorite,
        color: RamadanColors.bronze,
      ),
    ];
  }

  /// الحصول على التسبيحات الرمضانية
  List<RamadanTasbih> getRamadanTasbihs() {
    return [
      // أذكار ما بعد الإفطار
      const RamadanTasbih(
        id: 'after_iftar_1',
        title: 'تسبيح بعد الإفطار',
        arabicText:
            'سُبْحَانَ اللَّهِ وَالْحَمْدُ لِلَّهِ وَلَا إِلَهَ إِلَّا اللَّهُ وَاللَّهُ أَكْبَرُ',
        transliteration:
            'Subhan Allah wa al-hamdu lillah wa la ilaha illa Allah wa Allahu akbar',
        translation: 'سبحان الله والحمد لله ولا إله إلا الله والله أكبر',
        recommendedCount: 33,
        category: 'afterIftar',
        benefits: 'تطهير القلب وزيادة الأجر',
        icon: RamadanIcons.peace,
        color: RamadanColors.gold,
      ),

      // تسبيحات التراويح
      const RamadanTasbih(
        id: 'tarawih_1',
        title: 'تسبيح التراويح',
        arabicText: 'سُبْحَانَ ذِي الْمُلْكِ وَالْمَلَكُوتِ',
        transliteration: 'Subhan dhi al-mulki wa al-malakut',
        translation: 'سبحان ذي الملك والملكوت',
        recommendedCount: 10,
        category: 'tarawih',
        benefits: 'تعظيم الله في الصلاة',
        icon: RamadanIcons.mosque,
        color: RamadanColors.darkGreen,
      ),

      // أذكار الاعتكاف
      const RamadanTasbih(
        id: 'itikaf_1',
        title: 'ذكر الاعتكاف',
        arabicText:
            'لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ',
        transliteration:
            'La ilaha illa Allah wahdahu la sharika lahu lahu al-mulku wa lahu al-hamdu wa huwa ala kulli shayin qadir',
        translation:
            'لا إله إلا الله وحده لا شريك له له الملك وله الحمد وهو على كل شيء قدير',
        recommendedCount: 100,
        category: 'itikaf',
        source: 'البخاري ومسلم',
        benefits: 'أجر عظيم وتطهير للقلب',
        icon: RamadanIcons.book,
        color: RamadanColors.emerald,
      ),

      // تسبيحات رمضانية عامة
      const RamadanTasbih(
        id: 'general_1',
        title: 'تسبيح رمضاني',
        arabicText: 'اللَّهُمَّ بَلِّغْنَا رَمَضَانَ',
        transliteration: 'Allahumma ballighna Ramadan',
        translation: 'اللهم بلغنا رمضان',
        recommendedCount: 10,
        category: 'general',
        benefits: 'الدعاء ببلوغ رمضان',
        icon: RamadanIcons.crescent,
        color: RamadanColors.bronze,
      ),
    ];
  }

  /// تصفية الأدعية حسب الفئة
  List<RamadanDua> getDuasByCategory(String category) {
    return getRamadanDuas().where((dua) => dua.category == category).toList();
  }

  /// تصفية التسبيحات حسب الفئة
  List<RamadanTasbih> getTasbihsByCategory(String category) {
    return getRamadanTasbihs()
        .where((tasbih) => tasbih.category == category)
        .toList();
  }

  /// الحصول على دعاء أو تسبيحة عشوائية
  dynamic getRandomRamadanContent() {
    final duas = getRamadanDuas();
    final tasbihs = getRamadanTasbihs();
    final allContent = [...duas, ...tasbihs];

    if (allContent.isEmpty) return null;

    allContent.shuffle();
    return allContent.first;
  }
}
