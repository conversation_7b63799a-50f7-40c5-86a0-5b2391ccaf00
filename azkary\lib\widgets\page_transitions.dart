import 'package:flutter/material.dart';

/// أنواع انتقالات الصفحات
enum PageTransitionType {
  rightToLeftWithFade,
  leftToRightWithFade,
  topToBottomWithFade,
  bottomToTopWithFade,
  scale,
  fade,
  rightToLeft,
  leftToRight,
  topToBottom,
  bottomToTop,
}

/// فئة انتقال الصفحات
class PageTransition<T> extends PageRouteBuilder<T> {
  final Widget child;
  final PageTransitionType type;
  final Curve curve;
  final Alignment? alignment;
  final Duration duration;

  PageTransition({
    required this.child,
    required this.type,
    this.curve = Curves.linear,
    this.alignment,
    this.duration = const Duration(milliseconds: 300),
    super.settings,
  }) : super(
         pageBuilder: (context, animation, _) => child,
         transitionDuration: duration,
         transitionsBuilder: (context, animation, secondaryAnimation, child) {
           switch (type) {
             case PageTransitionType.fade:
               return FadeTransition(opacity: animation, child: child);

             case PageTransitionType.rightToLeftWithFade:
               return SlideTransition(
                 position: Tween<Offset>(
                   begin: const Offset(1.0, 0.0),
                   end: Offset.zero,
                 ).animate(CurvedAnimation(parent: animation, curve: curve)),
                 child: FadeTransition(opacity: animation, child: child),
               );

             case PageTransitionType.leftToRightWithFade:
               return SlideTransition(
                 position: Tween<Offset>(
                   begin: const Offset(-1.0, 0.0),
                   end: Offset.zero,
                 ).animate(CurvedAnimation(parent: animation, curve: curve)),
                 child: FadeTransition(opacity: animation, child: child),
               );

             case PageTransitionType.topToBottomWithFade:
               return SlideTransition(
                 position: Tween<Offset>(
                   begin: const Offset(0.0, -1.0),
                   end: Offset.zero,
                 ).animate(CurvedAnimation(parent: animation, curve: curve)),
                 child: FadeTransition(opacity: animation, child: child),
               );

             case PageTransitionType.bottomToTopWithFade:
               return SlideTransition(
                 position: Tween<Offset>(
                   begin: const Offset(0.0, 1.0),
                   end: Offset.zero,
                 ).animate(CurvedAnimation(parent: animation, curve: curve)),
                 child: FadeTransition(opacity: animation, child: child),
               );

             case PageTransitionType.scale:
               return ScaleTransition(
                 alignment: alignment ?? Alignment.center,
                 scale: CurvedAnimation(parent: animation, curve: curve),
                 child: child,
               );

             case PageTransitionType.rightToLeft:
               return SlideTransition(
                 position: Tween<Offset>(
                   begin: const Offset(1.0, 0.0),
                   end: Offset.zero,
                 ).animate(CurvedAnimation(parent: animation, curve: curve)),
                 child: child,
               );

             case PageTransitionType.leftToRight:
               return SlideTransition(
                 position: Tween<Offset>(
                   begin: const Offset(-1.0, 0.0),
                   end: Offset.zero,
                 ).animate(CurvedAnimation(parent: animation, curve: curve)),
                 child: child,
               );

             case PageTransitionType.topToBottom:
               return SlideTransition(
                 position: Tween<Offset>(
                   begin: const Offset(0.0, -1.0),
                   end: Offset.zero,
                 ).animate(CurvedAnimation(parent: animation, curve: curve)),
                 child: child,
               );

             case PageTransitionType.bottomToTop:
               return SlideTransition(
                 position: Tween<Offset>(
                   begin: const Offset(0.0, 1.0),
                   end: Offset.zero,
                 ).animate(CurvedAnimation(parent: animation, curve: curve)),
                 child: child,
               );
           }
         },
       );
}

/// دالة مساعدة للتنقل مع انتقالات مبسطة وسريعة
void navigateTo(
  BuildContext context,
  Widget destination, {
  PageTransitionType type = PageTransitionType.rightToLeftWithFade,
  Duration duration = const Duration(milliseconds: 200), // تقليل المدة
  Curve curve = Curves.easeOut, // منحنى أبسط
  bool replace = false,
}) {
  // استخدام MaterialPageRoute البسيط للأداء الأفضل
  final route = MaterialPageRoute(builder: (context) => destination);

  if (replace) {
    Navigator.pushReplacement(context, route);
  } else {
    Navigator.push(context, route);
  }
}
